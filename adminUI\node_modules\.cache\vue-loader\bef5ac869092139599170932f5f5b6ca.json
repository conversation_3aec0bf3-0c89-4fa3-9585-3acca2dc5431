{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\product\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\product\\list.vue", "mtime": 1754389997956}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport {\r\n  brandLstApi,\r\n  productListApi,\r\n  fetchProductApi,\r\n  batchPutOn,\r\n  batchPutoff,\r\n  batchDelete,\r\n  updateProductInfo,\r\n  newProduct\r\n} from \"@/api/brand\";\r\nimport { getToken } from \"@/utils/auth\";\r\nimport { checkPermi } from \"@/utils/permission\";\r\n\r\nexport default {\r\n  name: \"BrandProductList\",\r\n  data() {\r\n    return {\r\n      statusOptions: [\r\n        { value: null, label: \"all\" },      // 全部\r\n        { value: true, label: \"online\" },   // 上架\r\n        { value: false, label: \"offline\" }  // 下架\r\n      ],\r\n      typeOptions: [\r\n        { value: \"1\", label: this.$t(\"yes\") },\r\n        { value: \"0\", label: this.$t(\"no\") }\r\n      ],\r\n      loading: false,\r\n      listLoading: false,\r\n      tableData: {\r\n        data: [],\r\n        total: 0\r\n      },\r\n      form: {\r\n        page: 1,\r\n        limit: 20,\r\n        keywords: \"\",\r\n        isShow: null, // 使用 isShow 字段替代 type 字段：null=全部, true=上架, false=下架\r\n        total: 0,\r\n        isIndex: false,\r\n        brand: \"\"\r\n      },\r\n      url: \"\",\r\n      brandName: \"\",\r\n      // dform: {\r\n      //   id: '',\r\n      //   url: '',\r\n      //   storeName: '',\r\n      //   image: '',\r\n      //   price: '',\r\n      //   cashbackRate: '',\r\n      //   // cashbackAmount: '',\r\n      //   status: '',\r\n      // },\r\n      dform: {},\r\n      status: \"\",\r\n      productDialogVisible: false,\r\n      isEditMode: false, // 是否为编辑模式\r\n      multipleSelection: [],\r\n      brandOptions: [],\r\n      brandLoading: false // 品牌数据加载状态\r\n    };\r\n  },\r\n  mounted() {\r\n    var brand = this.$route.query.brand || \"\";\r\n    this.form.brand = brand;\r\n\r\n    this.getList();\r\n    this.getBrands();\r\n  },\r\n  watch: {\r\n    \"$route.query.brand\"(newBrand) {\r\n      this.form.brand = newBrand;\r\n      this.getList();\r\n    }\r\n  },\r\n  methods: {\r\n    checkPermi,\r\n    formatAmount(s){\r\n        if(s == undefined) {\r\n            s = 0\r\n        }\r\n        let s1 = (s/1000).toFixed(3)\r\n        return s1\r\n    },\r\n    getBrands() {\r\n      this.brandLoading = true;\r\n      brandLstApi({ page: 1, limit: 10000, type: \"-1\", name: \"\" })\r\n        .then(res => {\r\n          if (res.list) {\r\n            let options = [];\r\n            for (var i in res.list) {\r\n              var itm = res.list[i];\r\n              options.push({\r\n                label: itm[\"name\"],\r\n                value: itm[\"code\"]\r\n              });\r\n            }\r\n            this.brandOptions = options;\r\n            console.log(`成功获取 ${this.brandOptions.length} 个品牌数据`);\r\n          }\r\n          this.brandLoading = false;\r\n        })\r\n        .catch(res => {\r\n          this.brandLoading = false;\r\n          this.$message.error(this.$t(\"common.fetchDataFailed\") || \"获取品牌数据失败\");\r\n        });\r\n    },\r\n    // 手动刷新品牌数据\r\n    refreshBrands() {\r\n      this.getBrands();\r\n      this.$message.success(this.$t(\"brand.refreshingBrands\") || \"正在刷新品牌数据...\");\r\n    },\r\n    getList() {\r\n      let _this = this;\r\n      this.listLoading = true;\r\n      // 处理 isShow 参数：null 表示查询全部，不传递该参数\r\n      let params = { ...this.form };\r\n      if (params.isShow === null) {\r\n        delete params.isShow;\r\n      }\r\n      productListApi(params)\r\n        .then(res => {\r\n          _this.listLoading = false;\r\n          _this.tableData.data = res.list;\r\n          _this.tableData.total = res.total;\r\n        })\r\n        .catch(res => {\r\n          this.listLoading = false;\r\n          this.$message.error(this.$t(\"common.fetchDataFailed\"));\r\n        });\r\n    },\r\n    onSearch() {\r\n      this.form.page = 1;\r\n      this.getList();\r\n    },\r\n    onReset() {\r\n      this.form.keywords = \"\";\r\n      this.form.isShow = null; // 重置为全部状态\r\n      this.form.brand = \"\";\r\n      this.form.page = 1;\r\n      this.getList();\r\n    },\r\n    onAdd() {\r\n      // 每次打开新增弹窗时重新获取最新的品牌数据\r\n      this.getBrands();\r\n      this.isEditMode = false; // 设置为新增模式\r\n      this.productDialogVisible = true;\r\n      // 清空表单数据\r\n      this.dform = {};\r\n      this.brandName = '';\r\n      this.status = '';\r\n      this.url = '';\r\n    },\r\n    handleSelection(val) {\r\n      this.multipleSelection = val;\r\n    },\r\n    batchHandle(type) {\r\n      let _this = this;\r\n      let rows = [];\r\n      this.multipleSelection.forEach(row => {\r\n        rows.push(row.id);\r\n      });\r\n      if (rows.length > 0) {\r\n        this.listLoading = true;\r\n        let params = { ids: rows };\r\n        if (type === \"online\") {\r\n          batchPutOn(params).then(res => {\r\n            _this.getList();\r\n          });\r\n        } else if (type === \"outline\") {\r\n          batchPutoff(params).then(res => {\r\n            _this.getList();\r\n          });\r\n        } else if (type === \"delete\") {\r\n          params.type = \"recycle\";\r\n          batchDelete(params).then(res => {\r\n            _this.getList();\r\n          });\r\n        }\r\n      }\r\n    },\r\n    handleSizeChange(val) {\r\n      this.form.limit = val;\r\n      this.getList();\r\n    },\r\n    pageChange(page) {\r\n      this.form.page = page;\r\n      this.getList();\r\n    },\r\n    handleUpdate(row) {\r\n        let _this = this;\r\n        let params = { ids: [row.id] };\r\n        if (!Boolean(row.isShow)) {\r\n          batchPutoff(params).then(res => {\r\n            _this.getList();\r\n          }).catch(res=>{\r\n            _this.$message.error(this.$t(\"common.operationFailed\"));\r\n          });\r\n        } else {\r\n          batchPutOn(params).then(res => {\r\n            _this.getList();\r\n          }).catch(res=>{\r\n            _this.$message.error(this.$t(\"common.operationFailed\"));\r\n          });\r\n        }\r\n    },\r\n    formatTime(t) {\r\n      let date = new Date(t * 1000);\r\n      let year = date.getFullYear();\r\n      let month = String(date.getMonth() + 1).padStart(2, \"0\");\r\n      let day = String(date.getDate()).padStart(2, \"0\");\r\n      let hours = String(date.getHours()).padStart(2, \"0\");\r\n      let minutes = String(date.getMinutes()).padStart(2, \"0\");\r\n      let seconds = String(date.getSeconds()).padStart(2, \"0\");\r\n      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;\r\n    },\r\n    editProduct(row) {\r\n      // 每次打开编辑弹窗时重新获取最新的品牌数据\r\n      this.getBrands();\r\n      this.isEditMode = true; // 设置为编辑模式\r\n      this.productDialogVisible = true;\r\n      this.dform = row;\r\n      this.status = row.isShow ? \"1\" : \"0\";\r\n      this.brandName = row.brand;\r\n\r\n      this.dform.forMatCashBackRate=this.formatRate(this.dform.cashBackRate)\r\n      // this.dform.id = row.id\r\n      // this.dform.storeName = row.storeName\r\n      // this.dform.image = row.image\r\n      // this.dform.price = row.price\r\n      // this.dform.cashbackRate = row.cashbackRate\r\n      // this.dform.cashbackAmount = row.cashbackAmount\r\n      // this.dform.status = row.status === '1' ? '1' : '0'\r\n      // this.productDialogVisible = true\r\n    },\r\n    isShowChange(row, val, type) {\r\n      let _this = this;\r\n      // let rows = [];\r\n\r\n      let item = { id: row.id };\r\n      if (val) {\r\n        item[type] = true;\r\n      } else {\r\n        item[type] = false;\r\n      }\r\n      // rows.push(item);\r\n      updateProductInfo(item)\r\n        .then(res => {\r\n          //_this.getList();\r\n        })\r\n        .catch(res => {\r\n            this.$message.error(this.$t(\"common.operationFailed\"));\r\n        });\r\n    },\r\n    handleDelete(row) {\r\n      let _this = this;\r\n      this.$confirm(\r\n        this.$t(\"brand.confirmOperation\"),\r\n        this.$t(\"brand.prompt\"),\r\n        {\r\n          confirmButtonText: this.$t(\"brand.confirm\"),\r\n          cancelButtonText: this.$t(\"brand.cancel\"),\r\n          type: \"warning\",\r\n          showClose: false\r\n        }\r\n      ).then(() => {\r\n        let params = { ids: [row.id], type: \"recycle\" };\r\n        batchDelete(params).then(res => {\r\n          _this.getList();\r\n        });\r\n      });\r\n    },\r\n    handleCloseProductDialog() {\r\n      this.productDialogVisible = false;\r\n    },\r\n    onSubProduct() {\r\n      let _this = this;\r\n      if (this.dform && this.dform.id) {\r\n        updateProductInfo({\r\n          id: this.dform.id,\r\n          isShow: this.status == \"1\" ? true : false,\r\n          brand: this.brandName\r\n        })\r\n          .then(res => {\r\n            _this.getList();\r\n            _this.productDialogVisible = false;\r\n          })\r\n          .catch(res => {\r\n            _this.productDialogVisible = false;\r\n            this.$message.error(this.$t(\"common.operationFailed\"));\r\n          });\r\n      }\r\n    },\r\n    fetchProduct() {\r\n      let _this = this;\r\n      fetchProductApi(6, this.url)\r\n        .then(res => {\r\n          _this.dform = res;\r\n\r\n          _this.dform.cashBackRate = _this.formatRate(_this.dform.cashBackRate);\r\n\r\n          // this.dform.storeName = res.storeName\r\n          // this.dform.image = res.image\r\n          // this.dform.price = res.price\r\n          // this.dform.cashbackRate = res.cashBackRate\r\n          // this.dform.cashbackAmount = res.cashBackAmount\r\n        })\r\n        .catch(res => {\r\n          this.$message.error(this.$t(\"product.fetchProductFailed\"));\r\n        });\r\n    },\r\n    formatRate(s) {\r\n      return parseInt(s * 10000) / 100 + \"%\";\r\n    }\r\n  }\r\n};\r\n", null]}