{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\affiliate-products\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\affiliate-products\\index.vue", "mtime": 1754374966345}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _tiktok = require(\"@/api/tiktok\");\nvar _affiliateProducts = require(\"@/api/affiliate-products\");\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: 'AffiliateProducts',\n  data: function data() {\n    return {\n      loading: false,\n      tableData: [],\n      searchForm: {\n        keywords: [],\n        // 改为数组存储多个关键词\n        priceMin: '',\n        priceMax: '',\n        commissionMin: '',\n        commissionMax: '',\n        sortField: 'commission_rate',\n        sortOrder: 'DESC',\n        pageSize: 10 // 默认分页数量改为10\n      },\n      searchRules: {\n        // 移除关键词必填验证\n      },\n      currentCursor: '',\n      nextPageToken: '',\n      prevPageTokens: [],\n      // 存储前面页面的token，用于返回上一页\n      totalCount: 0,\n      hasNextPage: false,\n      hasPrevPage: false,\n      hasSearched: false,\n      // 标记是否已经搜索过\n      selectedProducts: [],\n      // 选中的商品列表\n      batchImporting: false,\n      // 批量入库状态\n      batchDeleting: false,\n      // 批量删除状态\n      importDialogVisible: false,\n      // 入库确认对话框\n      currentImportProduct: null,\n      // 当前导入的商品（单个导入时使用）\n      commissionErrors: {\n        // 佣金率验证错误信息\n        commissionMin: '',\n        commissionMax: ''\n      },\n      currentKeyword: '',\n      // 当前输入的关键词\n      keywordError: '' // 关键词验证错误信息\n    };\n  },\n  mounted: function mounted() {\n    // 页面加载时不自动搜索，等待用户点击查询按钮\n  },\n  methods: {\n    // 佣金率验证\n    validateCommissionRate: function validateCommissionRate(field) {\n      var value = this.searchForm[field];\n      if (value === '' || value === null || value === undefined) {\n        this.commissionErrors[field] = '';\n        return true;\n      }\n      var numValue = parseFloat(value);\n      if (isNaN(numValue)) {\n        this.commissionErrors[field] = this.$t('affiliateProducts.commissionInvalidNumber');\n        return false;\n      }\n      if (numValue !== 0 && numValue < 1000) {\n        this.commissionErrors[field] = this.$t('affiliateProducts.commissionRangeError');\n        return false;\n      }\n      this.commissionErrors[field] = '';\n      return true;\n    },\n    // 清除佣金率错误信息\n    clearCommissionError: function clearCommissionError(field) {\n      this.commissionErrors[field] = '';\n    },\n    // 添加关键词\n    addKeyword: function addKeyword() {\n      var keyword = this.currentKeyword.trim();\n      if (!keyword) {\n        this.keywordError = '';\n        return;\n      }\n\n      // 验证关键词数量限制\n      if (this.searchForm.keywords.length >= 20) {\n        this.keywordError = this.$t('affiliateProducts.keywordLimitExceeded');\n        return;\n      }\n\n      // 验证关键词长度\n      if (keyword.length > 255) {\n        this.keywordError = this.$t('affiliateProducts.keywordTooLong');\n        return;\n      }\n\n      // 检查是否重复\n      if (this.searchForm.keywords.includes(keyword)) {\n        this.keywordError = this.$t('affiliateProducts.keywordDuplicate');\n        return;\n      }\n\n      // 添加关键词\n      this.searchForm.keywords.push(keyword);\n      this.currentKeyword = '';\n      this.keywordError = '';\n    },\n    // 移除关键词\n    removeKeyword: function removeKeyword(index) {\n      this.searchForm.keywords.splice(index, 1);\n      this.keywordError = '';\n    },\n    // 清理所有关键词\n    clearAllKeywords: function clearAllKeywords() {\n      var _this = this;\n      this.$confirm(this.$t('affiliateProducts.confirmClearAllKeywords'), this.$t('affiliateProducts.clearAllKeywords'), {\n        confirmButtonText: this.$t('common.confirm'),\n        cancelButtonText: this.$t('common.cancel'),\n        type: 'warning'\n      }).then(function () {\n        _this.searchForm.keywords = [];\n        _this.keywordError = '';\n        _this.$message.success(_this.$t('affiliateProducts.keywordsClearedSuccess'));\n      }).catch(function () {\n        // 用户取消操作\n      });\n    },\n    // 搜索\n    handleSearch: function handleSearch() {\n      // 验证佣金率字段\n      var isCommissionMinValid = this.validateCommissionRate('commissionMin');\n      var isCommissionMaxValid = this.validateCommissionRate('commissionMax');\n      if (!isCommissionMinValid || !isCommissionMaxValid) {\n        this.$message.error(this.$t('affiliateProducts.pleaseFixErrors'));\n        return;\n      }\n      this.currentCursor = '';\n      this.nextPageToken = '';\n      this.prevPageTokens = [];\n      this.hasPrevPage = false;\n      this.hasSearched = true;\n      this.loadData();\n    },\n    // 重置\n    handleReset: function handleReset() {\n      this.searchForm = {\n        keywords: [],\n        // 重置为空数组\n        priceMin: '',\n        priceMax: '',\n        commissionMin: '',\n        commissionMax: '',\n        sortField: 'commission_rate',\n        sortOrder: 'DESC',\n        pageSize: 10 // 重置时也使用默认分页数量10\n      };\n      // 清除错误信息\n      this.commissionErrors = {\n        commissionMin: '',\n        commissionMax: ''\n      };\n      this.currentKeyword = '';\n      this.keywordError = '';\n      // 重置表单（无需验证状态重置）\n      // 清空表格数据\n      this.tableData = [];\n      this.hasSearched = false;\n      this.totalCount = 0;\n      this.hasNextPage = false;\n      this.hasPrevPage = false;\n      this.currentCursor = '';\n      this.nextPageToken = '';\n      this.prevPageTokens = [];\n    },\n    // 刷新\n    handleRefresh: function handleRefresh() {\n      if (this.hasSearched) {\n        this.loadData();\n      } else {\n        this.$message.warning(this.$t('affiliateProducts.searchFirst'));\n      }\n    },\n    // 下一页\n    handleNextPage: function handleNextPage() {\n      if (this.hasNextPage && this.nextPageToken) {\n        this.prevPageTokens.push(this.currentCursor);\n        this.currentCursor = this.nextPageToken;\n        this.hasPrevPage = true;\n        this.loadData();\n      }\n    },\n    // 上一页\n    handlePrevPage: function handlePrevPage() {\n      if (this.hasPrevPage && this.prevPageTokens.length > 0) {\n        this.currentCursor = this.prevPageTokens.pop();\n        if (this.prevPageTokens.length === 0) {\n          this.hasPrevPage = false;\n        }\n        this.loadData();\n      }\n    },\n    // 加载数据\n    loadData: function loadData() {\n      var _this2 = this;\n      this.loading = true;\n\n      // 构建请求参数 - 与V202405 AffiliateProductSearchRequest保持完全一致\n      var params = {\n        // 基础分页和排序参数\n        pageSize: this.searchForm.pageSize,\n        cursor: this.currentCursor,\n        sortField: this.searchForm.sortField,\n        sortOrder: this.searchForm.sortOrder,\n        // 搜索过滤参数（按V202405 API字段名）\n        titleKeywords: this.searchForm.keywords.length > 0 ? this.searchForm.keywords : null,\n        salesPriceMin: this.searchForm.priceMin ? parseFloat(this.searchForm.priceMin) : null,\n        salesPriceMax: this.searchForm.priceMax ? parseFloat(this.searchForm.priceMax) : null,\n        commissionRateMin: this.searchForm.commissionMin ? parseFloat(this.searchForm.commissionMin) : null,\n        commissionRateMax: this.searchForm.commissionMax ? parseFloat(this.searchForm.commissionMax) : null\n      };\n      (0, _tiktok.searchAffiliateProducts)(params).then(function (res) {\n        _this2.tableData = res.products || [];\n        _this2.nextPageToken = res.nextPageToken || '';\n        _this2.totalCount = res.totalCount || 0;\n        _this2.hasNextPage = !!_this2.nextPageToken;\n        if (_this2.tableData.length === 0) {\n          _this2.$message.info(_this2.$t('affiliateProducts.noResults'));\n        }\n      }).catch(function () {\n        _this2.loading = false;\n      }).finally(function () {\n        _this2.loading = false;\n      });\n    },\n    // 格式化价格\n    formatPrice: function formatPrice(priceObj) {\n      if (!priceObj) return '-';\n      var min = priceObj.minimumAmount;\n      var max = priceObj.maximumAmount;\n      var currency = priceObj.currency;\n      if (min === max) {\n        return \"\".concat(min, \" \").concat(currency);\n      } else {\n        return \"\".concat(min, \" - \").concat(max, \" \").concat(currency);\n      }\n    },\n    // 格式化佣金率（基点转百分比）\n    formatCommissionRate: function formatCommissionRate(rate) {\n      if (!rate) return '0';\n      return (rate / 100).toFixed(2);\n    },\n    // 处理选择变化\n    handleSelectionChange: function handleSelectionChange(selection) {\n      this.selectedProducts = selection;\n    },\n    // 单个商品导入\n    handleImportProduct: function handleImportProduct(product) {\n      // 设置当前操作的商品\n      this.currentImportProduct = product;\n      this.importDialogVisible = true;\n    },\n    // 单个商品删除\n    handleDeleteProduct: function handleDeleteProduct(product) {\n      var _this3 = this;\n      this.$confirm(this.$t('affiliateProducts.deleteConfirm'), this.$t('common.deleteConfirm'), {\n        confirmButtonText: this.$t('common.confirm'),\n        cancelButtonText: this.$t('common.cancel'),\n        type: 'warning'\n      }).then(function () {\n        product.deleting = true;\n        (0, _affiliateProducts.deleteAffiliateProducts)([product.id]).then(function () {\n          _this3.$message.success(_this3.$t('affiliateProducts.deleteSuccess'));\n        }).catch(function () {\n          // 错误消息已在响应拦截器中处理\n        }).finally(function () {\n          product.deleting = false;\n        });\n      }).catch(function () {\n        // 用户取消删除，不需要处理\n      });\n    },\n    // 批量导入\n    handleBatchImport: function handleBatchImport() {\n      if (this.selectedProducts.length === 0) {\n        this.$message.warning(this.$t('affiliateProducts.selectFirst'));\n        return;\n      }\n\n      // 设置批量导入模式\n      this.currentImportProduct = null;\n      this.importDialogVisible = true;\n    },\n    // 批量删除\n    handleBatchDelete: function handleBatchDelete() {\n      var _this4 = this;\n      if (this.selectedProducts.length === 0) {\n        this.$message.warning(this.$t('affiliateProducts.selectDeleteFirst'));\n        return;\n      }\n      this.$confirm(this.$t('affiliateProducts.batchDeleteConfirm', {\n        count: this.selectedProducts.length\n      }), this.$t('affiliateProducts.batchDelete'), {\n        confirmButtonText: this.$t('common.confirm'),\n        cancelButtonText: this.$t('common.cancel'),\n        type: 'warning'\n      }).then(function () {\n        _this4.batchDeleting = true;\n        var productIds = _this4.selectedProducts.map(function (p) {\n          return p.id;\n        });\n        (0, _affiliateProducts.deleteAffiliateProducts)(productIds).then(function () {\n          _this4.$message.success(_this4.$t('affiliateProducts.batchDeleteSuccess'));\n          // 清空选择\n          _this4.$refs.productTable.clearSelection();\n        }).catch(function () {\n          // 错误消息已在响应拦截器中处理\n        }).finally(function () {\n          _this4.batchDeleting = false;\n        });\n      }).catch(function () {\n        // 用户取消删除，不需要处理\n      });\n    },\n    // 确认导入\n    confirmImport: function confirmImport() {\n      var _this5 = this;\n      if (this.currentImportProduct) {\n        // 单个商品导入\n        this.currentImportProduct.importing = true;\n        this.importDialogVisible = false;\n        (0, _affiliateProducts.importAffiliateProducts)({\n          productIds: [this.currentImportProduct.id],\n          operationUser: this.$store.getters.name || 'admin'\n        }).then(function (data) {\n          // 根据导入结果显示不同的消息\n          if (data.failedCount === 0) {\n            if (data.skippedCount > 0) {\n              _this5.$message.warning(_this5.$t('affiliateProducts.importExists'));\n            } else {\n              _this5.$message.success(_this5.$t('affiliateProducts.importSuccess'));\n            }\n          } else {\n            _this5.$message.error(_this5.$t('affiliateProducts.importFailed', {\n              reason: data.failedProducts && data.failedProducts.length > 0 ? data.failedProducts[0].errorMessage : _this5.$t('common.unknownError')\n            }));\n          }\n        }).catch(function () {\n          // 错误消息已在响应拦截器中处理\n        }).finally(function () {\n          _this5.currentImportProduct.importing = false;\n        });\n      } else {\n        // 批量导入\n        this.batchImporting = true;\n        this.importDialogVisible = false;\n        var productIds = this.selectedProducts.map(function (p) {\n          return p.id;\n        });\n        (0, _affiliateProducts.importAffiliateProducts)({\n          productIds: productIds,\n          operationUser: this.$store.getters.name || 'admin'\n        }).then(function (data) {\n          // 根据导入结果显示不同的消息\n          if (data.failedCount === 0) {\n            if (data.skippedCount > 0 && data.successCount === 0) {\n              _this5.$message.warning(_this5.$t('affiliateProducts.batchImportExists'));\n            } else if (data.skippedCount > 0) {\n              _this5.$message.success(_this5.$t('affiliateProducts.batchImportMixed', {\n                success: data.successCount,\n                skipped: data.skippedCount\n              }));\n            } else {\n              _this5.$message.success(_this5.$t('affiliateProducts.batchImportSuccess', {\n                count: data.successCount\n              }));\n            }\n          } else if (data.successCount > 0) {\n            // 部分成功\n            _this5.$message.warning(_this5.$t('affiliateProducts.batchImportPartial', {\n              success: data.successCount,\n              failed: data.failedCount,\n              skipped: data.skippedCount\n            }));\n          } else {\n            // 全部失败\n            _this5.$message.error(_this5.$t('affiliateProducts.batchImportFailed', {\n              failed: data.failedCount,\n              skipped: data.skippedCount\n            }));\n          }\n\n          // 清空选择\n          _this5.$refs.productTable.clearSelection();\n        }).catch(function () {\n          // 错误消息已在响应拦截器中处理\n        }).finally(function () {\n          _this5.batchImporting = false;\n        });\n      }\n    }\n  }\n};", null]}