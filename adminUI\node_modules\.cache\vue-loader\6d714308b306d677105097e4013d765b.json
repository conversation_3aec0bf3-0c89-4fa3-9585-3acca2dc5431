{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1754375668239}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport ScrollPane from \"./ScrollPane\";\r\nimport path from \"path\";\r\n\r\nexport default {\r\n  components: { ScrollPane },\r\n  data() {\r\n    return {\r\n      fullWidth: document.body.clientWidth,\r\n      visible: false,\r\n      top: 0,\r\n      left: 0,\r\n      selectedTag: {},\r\n      affixTags: [],\r\n      isPhone: this.$wechat.isPhone()\r\n    };\r\n  },\r\n  computed: {\r\n    visitedViews() {\r\n      return this.$store.state.tagsView.visitedViews;\r\n    },\r\n    routes() {\r\n      return this.$store.state.permission.routes;\r\n    },\r\n    theme() {\r\n      return this.$store.state.settings.theme;\r\n    }\r\n  },\r\n  watch: {\r\n    $route() {\r\n      this.addTags();\r\n      if (!this.isPhone) this.moveToCurrentTag();\r\n    },\r\n    visible(value) {\r\n      if (value) {\r\n        document.body.addEventListener(\"click\", this.closeMenu);\r\n      } else {\r\n        document.body.removeEventListener(\"click\", this.closeMenu);\r\n      }\r\n    }\r\n  },\r\n  mounted() {\r\n    window.addEventListener(\"resize\", this.handleResize);\r\n    this.initTags();\r\n    this.addTags();\r\n  },\r\n  methods: {\r\n    handleResize(event) {\r\n      this.fullWidth = document.body.clientWidth;\r\n    },\r\n    isActive(route) {\r\n      return route.path === this.$route.path;\r\n    },\r\n    isAffix(tag) {\r\n      return tag.meta && tag.meta.affix;\r\n    },\r\n    filterAffixTags(routes, basePath = \"/\") {\r\n      let tags = [];\r\n      routes.forEach(route => {\r\n        if (route.meta && route.meta.affix) {\r\n          const tagPath = path.resolve(basePath, route.path);\r\n          tags.push({\r\n            fullPath: tagPath,\r\n            path: tagPath,\r\n            name: route.name,\r\n            meta: { ...route.meta }\r\n          });\r\n        }\r\n        if (route.children) {\r\n          const tempTags = this.filterAffixTags(route.children, route.path);\r\n          if (tempTags.length >= 1) {\r\n            tags = [...tags, ...tempTags];\r\n          }\r\n        }\r\n      });\r\n      return tags;\r\n    },\r\n    initTags() {\r\n      const affixTags = (this.affixTags = this.filterAffixTags(this.routes));\r\n      for (const tag of affixTags) {\r\n        // Must have tag name\r\n        if (tag.name) {\r\n          this.$store.dispatch(\"tagsView/addVisitedView\", tag);\r\n        }\r\n      }\r\n    },\r\n    addTags() {\r\n      const { name } = this.$route;\r\n      if (name) {\r\n        this.$store.dispatch(\"tagsView/addView\", this.$route);\r\n      }\r\n      return false;\r\n    },\r\n    moveToCurrentTag() {\r\n      const tags = this.$refs.tag;\r\n      this.$nextTick(() => {\r\n        for (const tag of tags) {\r\n          if (tag.to.path === this.$route.path) {\r\n            this.$refs.scrollPane.moveToTarget(tag);\r\n            // when query is different then update\r\n            if (tag.to.fullPath !== this.$route.fullPath) {\r\n              this.$store.dispatch(\"tagsView/updateVisitedView\", this.$route);\r\n            }\r\n            break;\r\n          }\r\n        }\r\n      });\r\n    },\r\n    refreshSelectedTag(view) {\r\n      this.$store.dispatch(\"tagsView/delCachedView\", view).then(() => {\r\n        const { fullPath } = view;\r\n        this.$nextTick(() => {\r\n          this.$router.replace({\r\n            path: \"/redirect\" + fullPath\r\n          });\r\n        });\r\n      });\r\n    },\r\n    closeSelectedTag(view) {\r\n      this.$store\r\n        .dispatch(\"tagsView/delView\", view)\r\n        .then(({ visitedViews }) => {\r\n          if (this.isActive(view)) {\r\n            this.toLastView(visitedViews, view);\r\n          }\r\n        });\r\n    },\r\n    closeOthersTags() {\r\n      this.$router.push(this.selectedTag);\r\n      this.$store\r\n        .dispatch(\"tagsView/delOthersViews\", this.selectedTag)\r\n        .then(() => {\r\n          this.moveToCurrentTag();\r\n        });\r\n    },\r\n    closeAllTags(view) {\r\n      this.$store.dispatch(\"tagsView/delAllViews\").then(({ visitedViews }) => {\r\n        if (this.affixTags.some(tag => tag.path === view.path)) {\r\n          return;\r\n        }\r\n        this.toLastView(visitedViews, view);\r\n      });\r\n    },\r\n    toLastView(visitedViews, view) {\r\n      const latestView = visitedViews.slice(-1)[0];\r\n      if (latestView) {\r\n        this.$router.push(latestView.fullPath);\r\n      } else {\r\n        // now the default is to redirect to the home page if there is no tags-view,\r\n        // you can adjust it according to your needs.\r\n        if (view.name === \"Dashboard\") {\r\n          // to reload home page\r\n          this.$router.replace({ path: \"/redirect\" + view.fullPath });\r\n        } else {\r\n          this.$router.push(\"/\");\r\n        }\r\n      }\r\n    },\r\n    openMenu(tag, e) {\r\n      const menuMinWidth = 105;\r\n      const offsetLeft = this.$el.getBoundingClientRect().left; // container margin left\r\n      const offsetWidth = this.$el.offsetWidth; // container width\r\n      const maxLeft = offsetWidth - menuMinWidth; // left boundary\r\n      const left = e.clientX - offsetLeft + 15; // 15: margin right\r\n\r\n      if (left > maxLeft) {\r\n        this.left = maxLeft;\r\n      } else {\r\n        this.left = left;\r\n      }\r\n\r\n      this.top = e.clientY;\r\n      this.visible = true;\r\n      this.selectedTag = tag;\r\n    },\r\n    closeMenu() {\r\n      this.visible = false;\r\n    }\r\n  }\r\n};\r\n", null]}