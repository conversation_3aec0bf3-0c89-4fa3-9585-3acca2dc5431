{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\product\\list.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\brand\\product\\list.vue", "mtime": 1754389632206}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _brand = require(\"@/api/brand\");\nvar _auth = require(\"@/utils/auth\");\nvar _permission = require(\"@/utils/permission\");\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  name: \"BrandProductList\",\n  data: function data() {\n    return {\n      statusOptions: [{\n        value: null,\n        label: \"all\"\n      },\n      // 全部\n      {\n        value: true,\n        label: \"online\"\n      },\n      // 上架\n      {\n        value: false,\n        label: \"offline\"\n      } // 下架\n      ],\n      typeOptions: [{\n        value: \"1\",\n        label: this.$t(\"yes\")\n      }, {\n        value: \"0\",\n        label: this.$t(\"no\")\n      }],\n      loading: false,\n      listLoading: false,\n      tableData: {\n        data: [],\n        total: 0\n      },\n      form: {\n        page: 1,\n        limit: 20,\n        keywords: \"\",\n        isShow: null,\n        // 使用 isShow 字段替代 type 字段：null=全部, true=上架, false=下架\n        total: 0,\n        isIndex: false,\n        brand: \"\"\n      },\n      url: \"\",\n      brandName: \"\",\n      // dform: {\n      //   id: '',\n      //   url: '',\n      //   storeName: '',\n      //   image: '',\n      //   price: '',\n      //   cashbackRate: '',\n      //   // cashbackAmount: '',\n      //   status: '',\n      // },\n      dform: {},\n      status: \"\",\n      productDialogVisible: false,\n      isEditMode: false,\n      // 是否为编辑模式\n      multipleSelection: [],\n      brandOptions: [],\n      brandLoading: false // 品牌数据加载状态\n    };\n  },\n  mounted: function mounted() {\n    var brand = this.$route.query.brand || \"\";\n    this.form.brand = brand;\n    this.getList();\n    this.getBrands();\n  },\n  watch: {\n    \"$route.query.brand\": function $routeQueryBrand(newBrand) {\n      this.form.brand = newBrand;\n      this.getList();\n    }\n  },\n  methods: {\n    checkPermi: _permission.checkPermi,\n    formatAmount: function formatAmount(s) {\n      if (s == undefined) {\n        s = 0;\n      }\n      var s1 = (s / 1000).toFixed(3);\n      return s1;\n    },\n    getBrands: function getBrands() {\n      var _this2 = this;\n      this.brandLoading = true;\n      (0, _brand.brandLstApi)({\n        page: 1,\n        limit: 10000,\n        type: \"-1\",\n        name: \"\"\n      }).then(function (res) {\n        if (res.list) {\n          var options = [];\n          for (var i in res.list) {\n            var itm = res.list[i];\n            options.push({\n              label: itm[\"name\"],\n              value: itm[\"code\"]\n            });\n          }\n          _this2.brandOptions = options;\n          console.log(\"\\u6210\\u529F\\u83B7\\u53D6 \".concat(_this2.brandOptions.length, \" \\u4E2A\\u54C1\\u724C\\u6570\\u636E\"));\n        }\n        _this2.brandLoading = false;\n      }).catch(function (res) {\n        _this2.brandLoading = false;\n        _this2.$message.error(_this2.$t(\"common.fetchDataFailed\") || \"获取品牌数据失败\");\n      });\n    },\n    // 手动刷新品牌数据\n    refreshBrands: function refreshBrands() {\n      this.getBrands();\n      this.$message.success(this.$t(\"brand.refreshingBrands\") || \"正在刷新品牌数据...\");\n    },\n    getList: function getList() {\n      var _this3 = this;\n      var _this = this;\n      this.listLoading = true;\n      // 处理 isShow 参数：null 表示查询全部，不传递该参数\n      var params = _objectSpread({}, this.form);\n      if (params.isShow === null) {\n        delete params.isShow;\n      }\n      (0, _brand.productListApi)(params).then(function (res) {\n        _this.listLoading = false;\n        _this.tableData.data = res.list;\n        _this.tableData.total = res.total;\n      }).catch(function (res) {\n        _this3.listLoading = false;\n        _this3.$message.error(_this3.$t(\"common.fetchDataFailed\"));\n      });\n    },\n    onSearch: function onSearch() {\n      this.form.page = 1;\n      this.getList();\n    },\n    onReset: function onReset() {\n      this.form.keywords = \"\";\n      this.form.isShow = null; // 重置为全部状态\n      this.form.brand = \"\";\n      this.form.page = 1;\n      this.getList();\n    },\n    onAdd: function onAdd() {\n      // 每次打开新增弹窗时重新获取最新的品牌数据\n      this.getBrands();\n      this.isEditMode = false; // 设置为新增模式\n      this.productDialogVisible = true;\n      // 清空表单数据\n      this.dform = {};\n      this.brandName = '';\n      this.status = '';\n      this.url = '';\n    },\n    handleSelection: function handleSelection(val) {\n      this.multipleSelection = val;\n    },\n    batchHandle: function batchHandle(type) {\n      var _this = this;\n      var rows = [];\n      this.multipleSelection.forEach(function (row) {\n        rows.push(row.id);\n      });\n      if (rows.length > 0) {\n        this.listLoading = true;\n        var params = {\n          ids: rows\n        };\n        if (type === \"online\") {\n          (0, _brand.batchPutOn)(params).then(function (res) {\n            _this.getList();\n          });\n        } else if (type === \"outline\") {\n          (0, _brand.batchPutoff)(params).then(function (res) {\n            _this.getList();\n          });\n        } else if (type === \"delete\") {\n          params.type = \"recycle\";\n          (0, _brand.batchDelete)(params).then(function (res) {\n            _this.getList();\n          });\n        }\n      }\n    },\n    handleSizeChange: function handleSizeChange(val) {\n      this.form.limit = val;\n      this.getList();\n    },\n    pageChange: function pageChange(page) {\n      this.form.page = page;\n      this.getList();\n    },\n    handleUpdate: function handleUpdate(row) {\n      var _this4 = this;\n      var _this = this;\n      var params = {\n        ids: [row.id]\n      };\n      if (!Boolean(row.isShow)) {\n        (0, _brand.batchPutoff)(params).then(function (res) {\n          //\n          // _this.getList();\n        }).catch(function (res) {\n          _this.$message.error(_this4.$t(\"common.operationFailed\"));\n        });\n      } else {\n        (0, _brand.batchPutOn)(params).then(function (res) {\n          // _this.getList();\n        }).catch(function (res) {\n          _this.$message.error(_this4.$t(\"common.operationFailed\"));\n        });\n      }\n    },\n    formatTime: function formatTime(t) {\n      var date = new Date(t * 1000);\n      var year = date.getFullYear();\n      var month = String(date.getMonth() + 1).padStart(2, \"0\");\n      var day = String(date.getDate()).padStart(2, \"0\");\n      var hours = String(date.getHours()).padStart(2, \"0\");\n      var minutes = String(date.getMinutes()).padStart(2, \"0\");\n      var seconds = String(date.getSeconds()).padStart(2, \"0\");\n      return \"\".concat(year, \"-\").concat(month, \"-\").concat(day, \" \").concat(hours, \":\").concat(minutes, \":\").concat(seconds);\n    },\n    editProduct: function editProduct(row) {\n      // 每次打开编辑弹窗时重新获取最新的品牌数据\n      this.getBrands();\n      this.isEditMode = true; // 设置为编辑模式\n      this.productDialogVisible = true;\n      this.dform = row;\n      this.status = row.isShow ? \"1\" : \"0\";\n      this.brandName = row.brand;\n      this.dform.forMatCashBackRate = this.formatRate(this.dform.cashBackRate);\n      // this.dform.id = row.id\n      // this.dform.storeName = row.storeName\n      // this.dform.image = row.image\n      // this.dform.price = row.price\n      // this.dform.cashbackRate = row.cashbackRate\n      // this.dform.cashbackAmount = row.cashbackAmount\n      // this.dform.status = row.status === '1' ? '1' : '0'\n      // this.productDialogVisible = true\n    },\n    isShowChange: function isShowChange(row, val, type) {\n      var _this5 = this;\n      var _this = this;\n      // let rows = [];\n\n      var item = {\n        id: row.id\n      };\n      if (val) {\n        item[type] = true;\n      } else {\n        item[type] = false;\n      }\n      // rows.push(item);\n      (0, _brand.updateProductInfo)(item).then(function (res) {\n        //_this.getList();\n      }).catch(function (res) {\n        _this5.$message.error(_this5.$t(\"common.operationFailed\"));\n      });\n    },\n    handleDelete: function handleDelete(row) {\n      var _this = this;\n      this.$confirm(this.$t(\"brand.confirmOperation\"), this.$t(\"brand.prompt\"), {\n        confirmButtonText: this.$t(\"brand.confirm\"),\n        cancelButtonText: this.$t(\"brand.cancel\"),\n        type: \"warning\",\n        showClose: false\n      }).then(function () {\n        var params = {\n          ids: [row.id],\n          type: \"recycle\"\n        };\n        (0, _brand.batchDelete)(params).then(function (res) {\n          _this.getList();\n        });\n      });\n    },\n    handleCloseProductDialog: function handleCloseProductDialog() {\n      this.productDialogVisible = false;\n    },\n    onSubProduct: function onSubProduct() {\n      var _this6 = this;\n      var _this = this;\n      if (this.dform && this.dform.id) {\n        (0, _brand.updateProductInfo)({\n          id: this.dform.id,\n          isShow: this.status == \"1\" ? true : false,\n          brand: this.brandName\n        }).then(function (res) {\n          _this.getList();\n          _this.productDialogVisible = false;\n        }).catch(function (res) {\n          _this.productDialogVisible = false;\n          _this6.$message.error(_this6.$t(\"common.operationFailed\"));\n        });\n      }\n    },\n    fetchProduct: function fetchProduct() {\n      var _this7 = this;\n      var _this = this;\n      (0, _brand.fetchProductApi)(6, this.url).then(function (res) {\n        _this.dform = res;\n        _this.dform.cashBackRate = _this.formatRate(_this.dform.cashBackRate);\n\n        // this.dform.storeName = res.storeName\n        // this.dform.image = res.image\n        // this.dform.price = res.price\n        // this.dform.cashbackRate = res.cashBackRate\n        // this.dform.cashbackAmount = res.cashBackAmount\n      }).catch(function (res) {\n        _this7.$message.error(_this7.$t(\"product.fetchProductFailed\"));\n      });\n    },\n    formatRate: function formatRate(s) {\n      return parseInt(s * 10000) / 100 + \"%\";\n    }\n  }\n};", null]}