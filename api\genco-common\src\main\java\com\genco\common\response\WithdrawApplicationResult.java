package com.genco.common.response;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现申请结果
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawApplicationResult {
    
    /**
     * 是否成功
     */
    private Boolean success;
    
    /**
     * 提现申请ID
     */
    private Integer withdrawId;
    
    /**
     * 申请金额
     */
    private BigDecimal requestAmount;
    
    /**
     * 手续费
     */
    private BigDecimal serviceFee;
    
    /**
     * 实际到账金额
     */
    private BigDecimal actualAmount;
    
    /**
     * 申请时间
     */
    private LocalDateTime applicationTime;
    
    /**
     * 预计到账时间
     */
    private LocalDateTime estimatedArrivalTime;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 错误代码
     */
    private String errorCode;
    
    /**
     * 用户剩余可提现金额
     */
    private BigDecimal remainingBalance;
    
    /**
     * 创建成功结果
     */
    public static WithdrawApplicationResult success(Integer withdrawId, BigDecimal requestAmount, 
                                                   BigDecimal serviceFee, BigDecimal actualAmount,
                                                   BigDecimal remainingBalance) {
        return WithdrawApplicationResult.builder()
                .success(true)
                .withdrawId(withdrawId)
                .requestAmount(requestAmount)
                .serviceFee(serviceFee)
                .actualAmount(actualAmount)
                .remainingBalance(remainingBalance)
                .applicationTime(LocalDateTime.now())
                .estimatedArrivalTime(LocalDateTime.now().plusHours(24))
                .build();
    }
    
    /**
     * 创建失败结果
     */
    public static WithdrawApplicationResult failure(String errorCode, String errorMessage) {
        return WithdrawApplicationResult.builder()
                .success(false)
                .errorCode(errorCode)
                .errorMessage(errorMessage)
                .build();
    }
}
