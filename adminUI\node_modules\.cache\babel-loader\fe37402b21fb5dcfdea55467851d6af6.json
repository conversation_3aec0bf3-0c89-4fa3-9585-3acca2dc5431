{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\TagsView\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1754375668239}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _ScrollPane = _interopRequireDefault(require(\"./ScrollPane\"));\nvar _path = _interopRequireDefault(require(\"path\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _createForOfIteratorHelper(r, e) { var t = \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"]; if (!t) { if (Array.isArray(r) || (t = _unsupportedIterableToArray(r)) || e && r && \"number\" == typeof r.length) { t && (r = t); var _n = 0, F = function F() {}; return { s: F, n: function n() { return _n >= r.length ? { done: !0 } : { done: !1, value: r[_n++] }; }, e: function e(r) { throw r; }, f: F }; } throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); } var o, a = !0, u = !1; return { s: function s() { t = t.call(r); }, n: function n() { var r = t.next(); return a = r.done, r; }, e: function e(r) { u = !0, o = r; }, f: function f() { try { a || null == t.return || t.return(); } finally { if (u) throw o; } } }; }\nfunction _toConsumableArray(r) { return _arrayWithoutHoles(r) || _iterableToArray(r) || _unsupportedIterableToArray(r) || _nonIterableSpread(); }\nfunction _nonIterableSpread() { throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\"); }\nfunction _unsupportedIterableToArray(r, a) { if (r) { if (\"string\" == typeof r) return _arrayLikeToArray(r, a); var t = {}.toString.call(r).slice(8, -1); return \"Object\" === t && r.constructor && (t = r.constructor.name), \"Map\" === t || \"Set\" === t ? Array.from(r) : \"Arguments\" === t || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(t) ? _arrayLikeToArray(r, a) : void 0; } }\nfunction _iterableToArray(r) { if (\"undefined\" != typeof Symbol && null != r[Symbol.iterator] || null != r[\"@@iterator\"]) return Array.from(r); }\nfunction _arrayWithoutHoles(r) { if (Array.isArray(r)) return _arrayLikeToArray(r); }\nfunction _arrayLikeToArray(r, a) { (null == a || a > r.length) && (a = r.length); for (var e = 0, n = Array(a); e < a; e++) n[e] = r[e]; return n; }\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nfunction _defineProperty(e, r, t) { return (r = _toPropertyKey(r)) in e ? Object.defineProperty(e, r, { value: t, enumerable: !0, configurable: !0, writable: !0 }) : e[r] = t, e; }\nfunction _toPropertyKey(t) { var i = _toPrimitive(t, \"string\"); return \"symbol\" == _typeof(i) ? i : i + \"\"; }\nfunction _toPrimitive(t, r) { if (\"object\" != _typeof(t) || !t) return t; var e = t[Symbol.toPrimitive]; if (void 0 !== e) { var i = e.call(t, r || \"default\"); if (\"object\" != _typeof(i)) return i; throw new TypeError(\"@@toPrimitive must return a primitive value.\"); } return (\"string\" === r ? String : Number)(t); } //\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  components: {\n    ScrollPane: _ScrollPane.default\n  },\n  data: function data() {\n    return {\n      fullWidth: document.body.clientWidth,\n      visible: false,\n      top: 0,\n      left: 0,\n      selectedTag: {},\n      affixTags: [],\n      isPhone: this.$wechat.isPhone()\n    };\n  },\n  computed: {\n    visitedViews: function visitedViews() {\n      return this.$store.state.tagsView.visitedViews;\n    },\n    routes: function routes() {\n      return this.$store.state.permission.routes;\n    },\n    theme: function theme() {\n      return this.$store.state.settings.theme;\n    }\n  },\n  watch: {\n    $route: function $route() {\n      this.addTags();\n      if (!this.isPhone) this.moveToCurrentTag();\n    },\n    visible: function visible(value) {\n      if (value) {\n        document.body.addEventListener(\"click\", this.closeMenu);\n      } else {\n        document.body.removeEventListener(\"click\", this.closeMenu);\n      }\n    }\n  },\n  mounted: function mounted() {\n    window.addEventListener(\"resize\", this.handleResize);\n    this.initTags();\n    this.addTags();\n  },\n  methods: {\n    handleResize: function handleResize(event) {\n      this.fullWidth = document.body.clientWidth;\n    },\n    isActive: function isActive(route) {\n      return route.path === this.$route.path;\n    },\n    isAffix: function isAffix(tag) {\n      return tag.meta && tag.meta.affix;\n    },\n    filterAffixTags: function filterAffixTags(routes) {\n      var _this = this;\n      var basePath = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : \"/\";\n      var tags = [];\n      routes.forEach(function (route) {\n        if (route.meta && route.meta.affix) {\n          var tagPath = _path.default.resolve(basePath, route.path);\n          tags.push({\n            fullPath: tagPath,\n            path: tagPath,\n            name: route.name,\n            meta: _objectSpread({}, route.meta)\n          });\n        }\n        if (route.children) {\n          var tempTags = _this.filterAffixTags(route.children, route.path);\n          if (tempTags.length >= 1) {\n            tags = [].concat(_toConsumableArray(tags), _toConsumableArray(tempTags));\n          }\n        }\n      });\n      return tags;\n    },\n    initTags: function initTags() {\n      var affixTags = this.affixTags = this.filterAffixTags(this.routes);\n      var _iterator = _createForOfIteratorHelper(affixTags),\n        _step;\n      try {\n        for (_iterator.s(); !(_step = _iterator.n()).done;) {\n          var tag = _step.value;\n          // Must have tag name\n          if (tag.name) {\n            this.$store.dispatch(\"tagsView/addVisitedView\", tag);\n          }\n        }\n      } catch (err) {\n        _iterator.e(err);\n      } finally {\n        _iterator.f();\n      }\n    },\n    addTags: function addTags() {\n      var name = this.$route.name;\n      if (name) {\n        this.$store.dispatch(\"tagsView/addView\", this.$route);\n      }\n      return false;\n    },\n    moveToCurrentTag: function moveToCurrentTag() {\n      var _this2 = this;\n      var tags = this.$refs.tag;\n      this.$nextTick(function () {\n        var _iterator2 = _createForOfIteratorHelper(tags),\n          _step2;\n        try {\n          for (_iterator2.s(); !(_step2 = _iterator2.n()).done;) {\n            var tag = _step2.value;\n            if (tag.to.path === _this2.$route.path) {\n              _this2.$refs.scrollPane.moveToTarget(tag);\n              // when query is different then update\n              if (tag.to.fullPath !== _this2.$route.fullPath) {\n                _this2.$store.dispatch(\"tagsView/updateVisitedView\", _this2.$route);\n              }\n              break;\n            }\n          }\n        } catch (err) {\n          _iterator2.e(err);\n        } finally {\n          _iterator2.f();\n        }\n      });\n    },\n    refreshSelectedTag: function refreshSelectedTag(view) {\n      var _this3 = this;\n      this.$store.dispatch(\"tagsView/delCachedView\", view).then(function () {\n        var fullPath = view.fullPath;\n        _this3.$nextTick(function () {\n          _this3.$router.replace({\n            path: \"/redirect\" + fullPath\n          });\n        });\n      });\n    },\n    closeSelectedTag: function closeSelectedTag(view) {\n      var _this4 = this;\n      this.$store.dispatch(\"tagsView/delView\", view).then(function (_ref) {\n        var visitedViews = _ref.visitedViews;\n        if (_this4.isActive(view)) {\n          _this4.toLastView(visitedViews, view);\n        }\n      });\n    },\n    closeOthersTags: function closeOthersTags() {\n      var _this5 = this;\n      this.$router.push(this.selectedTag);\n      this.$store.dispatch(\"tagsView/delOthersViews\", this.selectedTag).then(function () {\n        _this5.moveToCurrentTag();\n      });\n    },\n    closeAllTags: function closeAllTags(view) {\n      var _this6 = this;\n      this.$store.dispatch(\"tagsView/delAllViews\").then(function (_ref2) {\n        var visitedViews = _ref2.visitedViews;\n        if (_this6.affixTags.some(function (tag) {\n          return tag.path === view.path;\n        })) {\n          return;\n        }\n        _this6.toLastView(visitedViews, view);\n      });\n    },\n    toLastView: function toLastView(visitedViews, view) {\n      var latestView = visitedViews.slice(-1)[0];\n      if (latestView) {\n        this.$router.push(latestView.fullPath);\n      } else {\n        // now the default is to redirect to the home page if there is no tags-view,\n        // you can adjust it according to your needs.\n        if (view.name === \"Dashboard\") {\n          // to reload home page\n          this.$router.replace({\n            path: \"/redirect\" + view.fullPath\n          });\n        } else {\n          this.$router.push(\"/\");\n        }\n      }\n    },\n    openMenu: function openMenu(tag, e) {\n      var menuMinWidth = 105;\n      var offsetLeft = this.$el.getBoundingClientRect().left; // container margin left\n      var offsetWidth = this.$el.offsetWidth; // container width\n      var maxLeft = offsetWidth - menuMinWidth; // left boundary\n      var left = e.clientX - offsetLeft + 15; // 15: margin right\n\n      if (left > maxLeft) {\n        this.left = maxLeft;\n      } else {\n        this.left = left;\n      }\n      this.top = e.clientY;\n      this.visible = true;\n      this.selectedTag = tag;\n    },\n    closeMenu: function closeMenu() {\n      this.visible = false;\n    }\n  }\n};", null]}