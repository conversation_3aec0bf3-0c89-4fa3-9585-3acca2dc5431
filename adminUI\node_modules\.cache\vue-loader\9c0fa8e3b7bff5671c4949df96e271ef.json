{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\Sidebar\\index.vue?vue&type=template&id=33ec43fc", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1754380939666}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\n<div :class=\"{'has-logo':showLogo}\" :style=\"{ backgroundColor: variables.menuBackground }\">\n  <logo v-if=\"showLogo\" :collapse=\"isCollapse\" />\n  <el-scrollbar wrap-class=\"scrollbar-wrapper\">\n    <el-menu\n      :default-active=\"activeMenu\"\n      :collapse=\"isCollapse\"\n      :background-color=\"variables.menuBackground\"\n      :text-color=\" variables.menuColor\"\n      :unique-opened=\"true\"\n      :active-text-color=\"variables.menuActiveText\"\n      :collapse-transition=\"true\"\n      mode=\"vertical\"\n    >\n      <sidebar-item v-for=\"route in sidebarRouters\" :key=\"route.url\" :item=\"route\" :base-path=\"route.url\" /> \n    </el-menu>\n  </el-scrollbar>\n</div>\n", null]}