package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 平台级多语言奖励规则响应类
 */
@Data
@ApiModel(value = "PlatformRewardRulesResponse对象", description = "平台级多语言奖励规则响应")
public class PlatformRewardRulesResponse {

    @ApiModelProperty(value = "奖励规则中文描述")
    private String rewardRuleZh;

    @ApiModelProperty(value = "奖励规则英文描述")
    private String rewardRuleEn;

    @ApiModelProperty(value = "奖励规则印尼语描述")
    private String rewardRuleId;
}
