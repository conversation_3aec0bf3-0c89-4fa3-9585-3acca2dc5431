{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\financial\\detail\\index.vue?vue&type=template&id=48cc36f4&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\financial\\detail\\index.vue", "mtime": 1754368747215}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <el-card class=\"box-card\">\n    <el-tabs\n      v-model=\"tableFromType\"\n      @tab-click=\"getList(tableFromType, 1)\"\n      class=\"mb20\"\n    >\n      <el-tab-pane\n        :label=\"$t('financial.detail.purchaseDetail')\"\n        name=\"purchase\"\n      ></el-tab-pane>\n      <el-tab-pane\n        :label=\"$t('financial.detail.tradeDetail')\"\n        name=\"trade\"\n      ></el-tab-pane>\n    </el-tabs>\n    <div class=\"container mt-1\">\n      <el-form\n        v-if=\"tableFromType === 'purchase'\"\n        v-model=\"purchaseFrom\"\n        inline\n        size=\"small\"\n      >\n        <el-form-item :label=\"$t('financial.detail.rechargeType') + '：'\">\n          <el-select\n            v-model=\"purchaseFrom.rechargeType\"\n            :placeholder=\"$t('common.all')\"\n            clearable\n          >\n            <el-option\n              v-for=\"(item, index) in rechargeTypeList\"\n              :key=\"index\"\n              :label=\"$t('financial.detail.' + item.label)\"\n              :value=\"item.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item :label=\"$t('financial.detail.transactionTime') + '：'\">\n          <el-date-picker\n            v-model=\"timeList\"\n            :placeholder=\"$t('common.startDate')\"\n            clearable\n            value-format=\"yyyy-MM-dd\"\n            format=\"yyyy-MM-dd\"\n            size=\"small\"\n            type=\"daterange\"\n            placement=\"bottom-end\"\n            :start-placeholder=\"$t('common.startDate')\"\n            :end-placeholder=\"$t('common.endDate')\"\n            style=\"width: 250px;\"\n          />\n        </el-form-item>\n        <el-form-item :label=\"$t('financial.detail.paymentMethod') + '：'\">\n          <el-select\n            v-model=\"purchaseFrom.payChannel\"\n            :placeholder=\"$t('common.all')\"\n            clearable\n          >\n            <el-option\n              :label=\"$t('financial.detail.bankTransfer')\"\n              value=\"xendit\"\n            ></el-option>\n            <el-option\n              :label=\"$t('financial.detail.electronicWallet')\"\n              value=\"haipay\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n        <el-form-item\n          :label=\"$t('financial.detail.electronicWallet') + '：'\"\n          v-if=\"purchaseFrom.payChannel == 'haipay'\"\n        >\n          <el-select\n            v-model=\"purchaseFrom.walletCode\"\n            :placeholder=\"$t('common.all')\"\n            clearable\n          >\n            <el-option\n              v-for=\"item in walletList\"\n              :key=\"item.value\"\n              :label=\"item.label\"\n              :value=\"item.value\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n\n        <el-form-item\n          :label=\"$t('financial.detail.bankName') + '：'\"\n          v-if=\"purchaseFrom.payChannel == 'xendit'\"\n        >\n          <el-select\n            v-model=\"purchaseFrom.bankName\"\n            clearable\n            :placeholder=\"$t('common.all')\"\n          >\n            <el-option\n              v-for=\"(item, index) in bankList\"\n              :key=\"index\"\n              :label=\"item\"\n              :value=\"item\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n\n      <el-form v-else v-model=\"tradeFrom\" inline size=\"small\">\n        <el-form-item :label=\"$t('financial.detail.tradeNo') + '：'\">\n          <el-input\n            v-model=\"tradeFrom.linkId\"\n            size=\"small\"\n            :placeholder=\"$t('financial.detail.tradeNo')\"\n          ></el-input>\n        </el-form-item>\n        <el-form-item :label=\"$t('financial.detail.transactionTime') + '：'\">\n          <el-date-picker\n            v-model=\"timeList\"\n            value-format=\"yyyy-MM-dd\"\n            format=\"yyyy-MM-dd\"\n            size=\"small\"\n            type=\"daterange\"\n            placement=\"bottom-end\"\n            :start-placeholder=\"$t('common.startDate')\"\n            :end-placeholder=\"$t('common.endDate')\"\n            style=\"width: 250px;\"\n            clearable\n          />\n        </el-form-item>\n        <el-form-item :label=\"$t('financial.detail.tradeType') + '：'\">\n          <el-select\n            v-model=\"tradeFrom.type\"\n            :placeholder=\"$t('common.all')\"\n            clearable\n          >\n            <el-option\n              :label=\"$t('financial.detail.agentFee')\"\n              value=\"1\"\n            ></el-option>\n            <el-option\n              :label=\"$t('financial.detail.partnerFee')\"\n              value=\"2\"\n            ></el-option>\n          </el-select>\n        </el-form-item>\n      </el-form>\n    </div>\n\n    <el-button\n      size=\"small\"\n      type=\"primary\"\n      class=\"mr10\"\n      @click=\"getList(tableFromType)\"\n      >{{ $t(\"common.query\") }}</el-button\n    >\n\n    <el-button size=\"small\" type=\"\" class=\"mr10\" @click=\"handleReset()\">{{\n      $t(\"common.reset\")\n    }}</el-button>\n  </el-card>\n  <el-card class=\"box-card\" style=\"margin-top: 12px;\">\n    <div slot=\"header\" class=\"clearfix\">\n      <el-button\n        type=\"primary\"\n        size=\"small\"\n        @click=\"handleUpload\"\n        v-hasPermi=\"['admin:financialCenter:detail:upload']\"\n        >{{ $t(\"financial.detail.exportExcel\") }}</el-button\n      >\n    </div>\n    <el-table\n      v-if=\"tableFromType === 'purchase'\"\n      v-loading=\"loading\"\n      :data=\"purchaseTableData\"\n      size=\"small\"\n      :header-cell-style=\"{ fontWeight: 'bold' }\"\n    >\n      <el-table-column\n        type=\"index\"\n        :label=\"$t('common.serialNumber')\"\n        width=\"110\"\n      >\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.paymentTime')\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.payTime | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.paymentNo')\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.outTradeNo | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.rechargeType')\"\n        min-width=\"80\"\n      >\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.paymentAccount')\"\n        min-width=\"80\"\n      >\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.tradeAmount')\"\n        min-width=\"100\"\n      >\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.actualPaymentAmount')\"\n        min-width=\"80\"\n      >\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.paymentMethod')\"\n        min-width=\"100\"\n        prop=\"payChannel\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.payChannel | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.electronicWallet')\"\n        min-width=\"80\"\n      >\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.institutionNumber')\"\n        min-width=\"80\"\n      >\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.bankName')\"\n        min-width=\"80\"\n      >\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.paymentAccount')\"\n        min-width=\"80\"\n      >\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.mobile')\"\n        min-width=\"100\"\n        prop=\"phone\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.phone | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column :label=\"$t('financial.detail.payee')\" min-width=\"80\">\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.payeeAccount')\"\n        min-width=\"80\"\n      >\n      </el-table-column>\n    </el-table>\n    <el-pagination\n      v-if=\"tableFromType === 'purchase'\"\n      class=\"mt20\"\n      @size-change=\"e => sizeChange(e, 'purchase')\"\n      @current-change=\"e => pageChange(e, 'purchase')\"\n      :current-page=\"purchaseFrom.page\"\n      :page-sizes=\"[20, 40, 60, 100]\"\n      :page-size=\"purchaseFrom.limit\"\n      layout=\"total, sizes, prev, pager, next, jumper\"\n      :total=\"purchaseFrom.total\"\n    >\n    </el-pagination>\n    <el-table\n      v-if=\"tableFromType === 'trade'\"\n      v-loading=\"loading\"\n      :data=\"tradeTableData\"\n      size=\"small\"\n      :header-cell-style=\"{ fontWeight: 'bold' }\"\n    >\n      <el-table-column\n        type=\"index\"\n        :label=\"$t('common.serialNumber')\"\n        width=\"110\"\n      >\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.tradeNo')\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.linkId | filterEmpty\n        }}</template></el-table-column\n      >\n      <el-table-column\n        :label=\"$t('financial.detail.tradeType')\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.title | filterEmpty\n        }}</template>\n      </el-table-column>\n      <el-table-column\n        :label=\"$t('financial.detail.tradeAmount')\"\n        min-width=\"100\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.number | filterEmpty\n        }}</template></el-table-column\n      >\n      <el-table-column\n        :label=\"$t('financial.detail.transactionTime')\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.createTime | filterEmpty\n        }}</template></el-table-column\n      >\n      <el-table-column\n        :label=\"$t('financial.detail.userNickname')\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.nickName | filterEmpty\n        }}</template></el-table-column\n      >\n      <el-table-column\n        :label=\"$t('financial.detail.tikTokAccount')\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.tiktokAccount | filterEmpty\n        }}</template></el-table-column\n      >\n      <el-table-column\n        :label=\"$t('financial.detail.whatsApp')\"\n        min-width=\"80\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.whatsAppAccount | filterEmpty\n        }}</template></el-table-column\n      >\n      <el-table-column :label=\"$t('financial.detail.channel')\" min-width=\"80\">\n        <template slot-scope=\"scope\">{{\n          scope.row.channel | filterEmpty\n        }}</template></el-table-column\n      >\n      <el-table-column\n        :label=\"$t('financial.detail.orderNo')\"\n        min-width=\"150\"\n      >\n        <template slot-scope=\"scope\">{{\n          scope.row.linkId | filterEmpty\n        }}</template></el-table-column\n      >\n    </el-table>\n    <el-pagination\n      v-if=\"tableFromType === 'trade'\"\n      class=\"mt20\"\n      @size-change=\"e => sizeChange(e, 'trade')\"\n      @current-change=\"e => pageChange(e, 'trade')\"\n      :current-page=\"tradeFrom.page\"\n      :page-sizes=\"[20, 40, 60, 100]\"\n      :page-size=\"tradeFrom.limit\"\n      layout=\"total, sizes, prev, pager, next, jumper\"\n      :total=\"tradeFrom.total\"\n    >\n    </el-pagination>\n  </el-card>\n</div>\n", null]}