{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\affiliate-products\\index.vue?vue&type=template&id=76995300&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\affiliate-products\\index.vue", "mtime": 1754374966345}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox relative\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"el-form\",\n            {\n              ref: \"searchFormRef\",\n              attrs: {\n                model: _vm.searchForm,\n                rules: _vm.searchRules,\n                inline: \"\",\n                size: \"small\",\n              },\n            },\n            [\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"affiliateProducts.priceRange\") } },\n                [\n                  _c(\"el-input\", {\n                    staticStyle: { width: \"120px\" },\n                    attrs: {\n                      placeholder: _vm.$t(\"affiliateProducts.minPrice\"),\n                    },\n                    model: {\n                      value: _vm.searchForm.priceMin,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.searchForm, \"priceMin\", $$v)\n                      },\n                      expression: \"searchForm.priceMin\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"span\", { staticStyle: { margin: \"0 8px\" } }, [\n                    _vm._v(\"-\"),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"el-input\", {\n                    staticStyle: { width: \"120px\" },\n                    attrs: {\n                      placeholder: _vm.$t(\"affiliateProducts.maxPrice\"),\n                    },\n                    model: {\n                      value: _vm.searchForm.priceMax,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.searchForm, \"priceMax\", $$v)\n                      },\n                      expression: \"searchForm.priceMax\",\n                    },\n                  }),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                {\n                  attrs: {\n                    label: _vm.$t(\"affiliateProducts.commissionRange\"),\n                    prop: \"commissionRange\",\n                  },\n                },\n                [\n                  _c(\"el-input\", {\n                    staticStyle: { width: \"120px\" },\n                    attrs: {\n                      placeholder: _vm.$t(\"affiliateProducts.minCommission\"),\n                    },\n                    on: {\n                      blur: function ($event) {\n                        return _vm.validateCommissionRate(\"commissionMin\")\n                      },\n                      input: function ($event) {\n                        return _vm.clearCommissionError(\"commissionMin\")\n                      },\n                    },\n                    model: {\n                      value: _vm.searchForm.commissionMin,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.searchForm, \"commissionMin\", $$v)\n                      },\n                      expression: \"searchForm.commissionMin\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"span\", { staticStyle: { margin: \"0 8px\" } }, [\n                    _vm._v(\"-\"),\n                  ]),\n                  _vm._v(\" \"),\n                  _c(\"el-input\", {\n                    staticStyle: { width: \"120px\" },\n                    attrs: {\n                      placeholder: _vm.$t(\"affiliateProducts.maxCommission\"),\n                    },\n                    on: {\n                      blur: function ($event) {\n                        return _vm.validateCommissionRate(\"commissionMax\")\n                      },\n                      input: function ($event) {\n                        return _vm.clearCommissionError(\"commissionMax\")\n                      },\n                    },\n                    model: {\n                      value: _vm.searchForm.commissionMax,\n                      callback: function ($$v) {\n                        _vm.$set(_vm.searchForm, \"commissionMax\", $$v)\n                      },\n                      expression: \"searchForm.commissionMax\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _vm.commissionErrors.commissionMin\n                    ? _c(\"div\", { staticClass: \"commission-error\" }, [\n                        _vm._v(\n                          \"\\n          \" +\n                            _vm._s(_vm.commissionErrors.commissionMin) +\n                            \"\\n        \"\n                        ),\n                      ])\n                    : _vm._e(),\n                  _vm._v(\" \"),\n                  _vm.commissionErrors.commissionMax\n                    ? _c(\"div\", { staticClass: \"commission-error\" }, [\n                        _vm._v(\n                          \"\\n          \" +\n                            _vm._s(_vm.commissionErrors.commissionMax) +\n                            \"\\n        \"\n                        ),\n                      ])\n                    : _vm._e(),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"affiliateProducts.sort\") } },\n                [\n                  _c(\n                    \"el-select\",\n                    {\n                      staticStyle: { width: \"140px\" },\n                      model: {\n                        value: _vm.searchForm.sortField,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.searchForm, \"sortField\", $$v)\n                        },\n                        expression: \"searchForm.sortField\",\n                      },\n                    },\n                    [\n                      _c(\"el-option\", {\n                        attrs: {\n                          label: _vm.$t(\"affiliateProducts.sortCommissionRate\"),\n                          value: \"commission_rate\",\n                        },\n                      }),\n                      _vm._v(\" \"),\n                      _c(\"el-option\", {\n                        attrs: {\n                          label: _vm.$t(\"affiliateProducts.sortCommission\"),\n                          value: \"commission\",\n                        },\n                      }),\n                      _vm._v(\" \"),\n                      _c(\"el-option\", {\n                        attrs: {\n                          label: _vm.$t(\"affiliateProducts.sortPrice\"),\n                          value: \"product_sales_price\",\n                        },\n                      }),\n                      _vm._v(\" \"),\n                      _c(\"el-option\", {\n                        attrs: {\n                          label: _vm.$t(\"affiliateProducts.sortSales\"),\n                          value: \"units_sold\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-select\",\n                    {\n                      staticStyle: { width: \"80px\", \"margin-left\": \"8px\" },\n                      model: {\n                        value: _vm.searchForm.sortOrder,\n                        callback: function ($$v) {\n                          _vm.$set(_vm.searchForm, \"sortOrder\", $$v)\n                        },\n                        expression: \"searchForm.sortOrder\",\n                      },\n                    },\n                    [\n                      _c(\"el-option\", {\n                        attrs: {\n                          label: _vm.$t(\"affiliateProducts.sortDesc\"),\n                          value: \"DESC\",\n                        },\n                      }),\n                      _vm._v(\" \"),\n                      _c(\"el-option\", {\n                        attrs: {\n                          label: _vm.$t(\"affiliateProducts.sortAsc\"),\n                          value: \"ASC\",\n                        },\n                      }),\n                    ],\n                    1\n                  ),\n                ],\n                1\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-form-item\",\n                { attrs: { label: _vm.$t(\"affiliateProducts.keywords\") } },\n                [\n                  _c(\n                    \"el-input\",\n                    {\n                      staticStyle: { width: \"300px\" },\n                      attrs: {\n                        placeholder: _vm.$t(\n                          \"affiliateProducts.addKeywordPlaceholder\"\n                        ),\n                        clearable: \"\",\n                        disabled: _vm.searchForm.keywords.length >= 20,\n                      },\n                      nativeOn: {\n                        keyup: function ($event) {\n                          if (\n                            !$event.type.indexOf(\"key\") &&\n                            _vm._k(\n                              $event.keyCode,\n                              \"enter\",\n                              13,\n                              $event.key,\n                              \"Enter\"\n                            )\n                          ) {\n                            return null\n                          }\n                          return _vm.addKeyword($event)\n                        },\n                      },\n                      model: {\n                        value: _vm.currentKeyword,\n                        callback: function ($$v) {\n                          _vm.currentKeyword = $$v\n                        },\n                        expression: \"currentKeyword\",\n                      },\n                    },\n                    [\n                      _c(\"template\", { slot: \"append\" }, [\n                        _c(\n                          \"span\",\n                          {\n                            staticClass: \"keyword-count\",\n                            class: {\n                              \"keyword-count-warning\":\n                                _vm.searchForm.keywords.length >= 18,\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n              \" +\n                                _vm._s(_vm.searchForm.keywords.length) +\n                                \"/20\\n            \"\n                            ),\n                          ]\n                        ),\n                      ]),\n                    ],\n                    2\n                  ),\n                  _vm._v(\" \"),\n                  _vm.keywordError\n                    ? _c(\n                        \"div\",\n                        {\n                          staticClass: \"keyword-error\",\n                          staticStyle: { \"margin-top\": \"4px\" },\n                        },\n                        [\n                          _vm._v(\n                            \"\\n          \" +\n                              _vm._s(_vm.keywordError) +\n                              \"\\n        \"\n                          ),\n                        ]\n                      )\n                    : _vm._e(),\n                ],\n                1\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"search-actions\" },\n            [\n              _c(\n                \"el-button\",\n                { attrs: { type: \"primary\" }, on: { click: _vm.handleSearch } },\n                [_vm._v(_vm._s(_vm.$t(\"affiliateProducts.query\")))]\n              ),\n              _vm._v(\" \"),\n              _c(\"el-button\", { on: { click: _vm.handleReset } }, [\n                _vm._v(_vm._s(_vm.$t(\"affiliateProducts.reset\"))),\n              ]),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _vm.searchForm.keywords.length > 0\n            ? _c(\n                \"div\",\n                {\n                  staticClass: \"keywords-display-section\",\n                  staticStyle: { \"margin-top\": \"16px\" },\n                },\n                [\n                  _c(\n                    \"div\",\n                    { staticClass: \"keywords-display-header\" },\n                    [\n                      _c(\"div\", { staticClass: \"keywords-display-label\" }, [\n                        _vm._v(\n                          _vm._s(_vm.$t(\"affiliateProducts.selectedKeywords\"))\n                        ),\n                      ]),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-button\",\n                        {\n                          staticClass: \"clear-all-btn\",\n                          attrs: {\n                            size: \"mini\",\n                            type: \"text\",\n                            icon: \"el-icon-delete\",\n                            title: _vm.$t(\"affiliateProducts.clearAllKeywords\"),\n                          },\n                          on: { click: _vm.clearAllKeywords },\n                        },\n                        [\n                          _vm._v(\n                            \"\\n          \" +\n                              _vm._s(_vm.$t(\"affiliateProducts.clearAll\")) +\n                              \"\\n        \"\n                          ),\n                        ]\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"div\",\n                    { staticClass: \"keywords-display-container\" },\n                    _vm._l(_vm.searchForm.keywords, function (keyword, index) {\n                      return _c(\n                        \"el-tag\",\n                        {\n                          key: index,\n                          staticClass: \"keyword-display-tag\",\n                          attrs: { closable: \"\" },\n                          on: {\n                            close: function ($event) {\n                              return _vm.removeKeyword(index)\n                            },\n                          },\n                        },\n                        [\n                          _vm._v(\n                            \"\\n          \" + _vm._s(keyword) + \"\\n        \"\n                          ),\n                        ]\n                      )\n                    }),\n                    1\n                  ),\n                ]\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\", staticStyle: { \"margin-top\": \"12px\" } },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\"span\", [\n                _vm._v(_vm._s(_vm.$t(\"affiliateProducts.listTitle\"))),\n              ]),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  staticStyle: { float: \"right\", padding: \"3px 0\" },\n                  attrs: { type: \"text\" },\n                  on: { click: _vm.handleRefresh },\n                },\n                [_vm._v(_vm._s(_vm.$t(\"affiliateProducts.refresh\")))]\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _vm.hasSearched && _vm.tableData.length > 0\n            ? _c(\n                \"div\",\n                { staticStyle: { \"margin-bottom\": \"15px\" } },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: {\n                        type: \"primary\",\n                        size: \"small\",\n                        disabled:\n                          _vm.selectedProducts.length === 0 ||\n                          _vm.batchImporting,\n                      },\n                      on: { click: _vm.handleBatchImport },\n                    },\n                    [\n                      _vm._v(\n                        \"\\n        \" +\n                          _vm._s(\n                            _vm.batchImporting\n                              ? _vm.$t(\"affiliateProducts.batchImporting\")\n                              : _vm.$t(\"affiliateProducts.batchImport\") +\n                                  \" (\" +\n                                  _vm.selectedProducts.length +\n                                  \")\"\n                          ) +\n                          \"\\n      \"\n                      ),\n                    ]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: {\n                        type: \"danger\",\n                        size: \"small\",\n                        disabled:\n                          _vm.selectedProducts.length === 0 ||\n                          _vm.batchDeleting,\n                      },\n                      on: { click: _vm.handleBatchDelete },\n                    },\n                    [\n                      _vm._v(\n                        \"\\n        \" +\n                          _vm._s(\n                            _vm.batchDeleting\n                              ? _vm.$t(\"affiliateProducts.batchDeleting\")\n                              : _vm.$t(\"affiliateProducts.batchDelete\") +\n                                  \" (\" +\n                                  _vm.selectedProducts.length +\n                                  \")\"\n                          ) +\n                          \"\\n      \"\n                      ),\n                    ]\n                  ),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          !_vm.hasSearched && _vm.tableData.length === 0\n            ? _c(\n                \"div\",\n                { staticClass: \"empty-tip\" },\n                [\n                  _c(\"el-empty\", {\n                    attrs: {\n                      description: _vm.$t(\"affiliateProducts.emptyTip\"),\n                    },\n                  }),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _c(\n            \"el-table\",\n            {\n              directives: [\n                {\n                  name: \"loading\",\n                  rawName: \"v-loading\",\n                  value: _vm.loading,\n                  expression: \"loading\",\n                },\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.hasSearched,\n                  expression: \"hasSearched\",\n                },\n              ],\n              ref: \"productTable\",\n              attrs: {\n                data: _vm.tableData,\n                size: \"small\",\n                \"header-cell-style\": { fontWeight: \"bold\" },\n              },\n              on: { \"selection-change\": _vm.handleSelectionChange },\n            },\n            [\n              _c(\"el-table-column\", {\n                attrs: { type: \"selection\", width: \"55\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  type: \"index\",\n                  label: _vm.$t(\"affiliateProducts.serialNumber\"),\n                  width: \"60\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.productImage\"),\n                  width: \"100\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-image\",\n                          {\n                            staticStyle: {\n                              width: \"60px\",\n                              height: \"60px\",\n                              \"border-radius\": \"4px\",\n                            },\n                            attrs: {\n                              src: scope.row.mainImageUrl,\n                              fit: \"cover\",\n                              \"preview-src-list\": [scope.row.mainImageUrl],\n                            },\n                          },\n                          [\n                            _c(\n                              \"div\",\n                              {\n                                staticClass: \"image-slot\",\n                                attrs: { slot: \"error\" },\n                                slot: \"error\",\n                              },\n                              [\n                                _c(\"i\", {\n                                  staticClass: \"el-icon-picture-outline\",\n                                }),\n                              ]\n                            ),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.productTitle\"),\n                  \"min-width\": \"200\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-link\",\n                          {\n                            attrs: {\n                              href: scope.row.detailLink,\n                              target: \"_blank\",\n                              type: \"primary\",\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(scope.row.title) +\n                                \"\\n          \"\n                            ),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.shop\"),\n                  width: \"120\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _vm._v(\n                          \"\\n          \" +\n                            _vm._s(scope.row.shop ? scope.row.shop.name : \"-\") +\n                            \"\\n        \"\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.originalPrice\"),\n                  width: \"100\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        scope.row.originalPrice\n                          ? _c(\"span\", [\n                              _vm._v(\n                                \"\\n            \" +\n                                  _vm._s(\n                                    _vm.formatPrice(scope.row.originalPrice)\n                                  ) +\n                                  \"\\n          \"\n                              ),\n                            ])\n                          : _c(\"span\", [_vm._v(\"-\")]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.salesPrice\"),\n                  width: \"100\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        scope.row.salesPrice\n                          ? _c(\"span\", [\n                              _vm._v(\n                                \"\\n            \" +\n                                  _vm._s(\n                                    _vm.formatPrice(scope.row.salesPrice)\n                                  ) +\n                                  \"\\n          \"\n                              ),\n                            ])\n                          : _c(\"span\", [_vm._v(\"-\")]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.commissionRate\"),\n                  width: \"80\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        scope.row.commission\n                          ? _c(\"span\", [\n                              _vm._v(\n                                \"\\n            \" +\n                                  _vm._s(\n                                    _vm.formatCommissionRate(\n                                      scope.row.commission.rate\n                                    )\n                                  ) +\n                                  \"%\\n          \"\n                              ),\n                            ])\n                          : _c(\"span\", [_vm._v(\"-\")]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.commissionAmount\"),\n                  width: \"100\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        scope.row.commission\n                          ? _c(\"span\", [\n                              _vm._v(\n                                \"\\n            \" +\n                                  _vm._s(scope.row.commission.amount) +\n                                  \" \" +\n                                  _vm._s(scope.row.commission.currency) +\n                                  \"\\n          \"\n                              ),\n                            ])\n                          : _c(\"span\", [_vm._v(\"-\")]),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.unitsSold\"),\n                  width: \"80\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _vm._v(\n                          \"\\n          \" +\n                            _vm._s(scope.row.unitsSold || 0) +\n                            \"\\n        \"\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.inventoryStatus\"),\n                  width: \"80\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-tag\",\n                          {\n                            attrs: {\n                              type: scope.row.hasInventory\n                                ? \"success\"\n                                : \"danger\",\n                              size: \"mini\",\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(\n                                  scope.row.hasInventory\n                                    ? _vm.$t(\"affiliateProducts.hasInventory\")\n                                    : _vm.$t(\"affiliateProducts.noInventory\")\n                                ) +\n                                \"\\n          \"\n                            ),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.saleRegion\"),\n                  width: \"80\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _vm._v(\n                          \"\\n          \" +\n                            _vm._s(scope.row.saleRegion || \"-\") +\n                            \"\\n        \"\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.importStatus\"),\n                  width: \"80\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-tag\",\n                          {\n                            attrs: {\n                              type: scope.row.isImported ? \"success\" : \"info\",\n                              size: \"mini\",\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(\n                                  scope.row.isImported\n                                    ? _vm.$t(\"affiliateProducts.imported\")\n                                    : _vm.$t(\"affiliateProducts.notImported\")\n                                ) +\n                                \"\\n          \"\n                            ),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n              _vm._v(\" \"),\n              _c(\"el-table-column\", {\n                attrs: {\n                  label: _vm.$t(\"affiliateProducts.action\"),\n                  width: \"180\",\n                  fixed: \"right\",\n                },\n                scopedSlots: _vm._u([\n                  {\n                    key: \"default\",\n                    fn: function (scope) {\n                      return [\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: {\n                              type: scope.row.isImported\n                                ? \"success\"\n                                : \"primary\",\n                              size: \"mini\",\n                              disabled:\n                                scope.row.importing || scope.row.isImported,\n                            },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleImportProduct(scope.row)\n                              },\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(\n                                  scope.row.importing\n                                    ? _vm.$t(\"affiliateProducts.importing\")\n                                    : scope.row.isImported\n                                    ? _vm.$t(\"affiliateProducts.imported\")\n                                    : _vm.$t(\"affiliateProducts.import\")\n                                ) +\n                                \"\\n          \"\n                            ),\n                          ]\n                        ),\n                        _vm._v(\" \"),\n                        _c(\n                          \"el-button\",\n                          {\n                            attrs: {\n                              type: \"danger\",\n                              size: \"mini\",\n                              disabled: scope.row.deleting,\n                            },\n                            on: {\n                              click: function ($event) {\n                                return _vm.handleDeleteProduct(scope.row)\n                              },\n                            },\n                          },\n                          [\n                            _vm._v(\n                              \"\\n            \" +\n                                _vm._s(\n                                  scope.row.deleting\n                                    ? _vm.$t(\"affiliateProducts.deleting\")\n                                    : _vm.$t(\"affiliateProducts.delete\")\n                                ) +\n                                \"\\n          \"\n                            ),\n                          ]\n                        ),\n                      ]\n                    },\n                  },\n                ]),\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _vm.hasSearched\n            ? _c(\n                \"div\",\n                {\n                  staticClass: \"pagination-container\",\n                  staticStyle: { \"margin-top\": \"20px\", \"text-align\": \"center\" },\n                },\n                [\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { disabled: !_vm.hasPrevPage, size: \"small\" },\n                      on: { click: _vm.handlePrevPage },\n                    },\n                    [_vm._v(_vm._s(_vm.$t(\"affiliateProducts.prevPage\")))]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"el-button\",\n                    {\n                      attrs: { disabled: !_vm.hasNextPage, size: \"small\" },\n                      on: { click: _vm.handleNextPage },\n                    },\n                    [_vm._v(_vm._s(_vm.$t(\"affiliateProducts.nextPage\")))]\n                  ),\n                  _vm._v(\" \"),\n                  _c(\n                    \"span\",\n                    { staticStyle: { \"margin-left\": \"20px\" } },\n                    [\n                      _vm._v(\n                        \"\\n        \" +\n                          _vm._s(_vm.$t(\"affiliateProducts.pageSize\")) +\n                          \"\\n        \"\n                      ),\n                      _c(\n                        \"el-select\",\n                        {\n                          staticStyle: { width: \"80px\" },\n                          attrs: { size: \"mini\" },\n                          on: { change: _vm.handleSearch },\n                          model: {\n                            value: _vm.searchForm.pageSize,\n                            callback: function ($$v) {\n                              _vm.$set(_vm.searchForm, \"pageSize\", $$v)\n                            },\n                            expression: \"searchForm.pageSize\",\n                          },\n                        },\n                        [\n                          _c(\"el-option\", {\n                            attrs: { label: \"10\", value: 10 },\n                          }),\n                          _vm._v(\" \"),\n                          _c(\"el-option\", {\n                            attrs: { label: \"20\", value: 20 },\n                          }),\n                          _vm._v(\" \"),\n                          _c(\"el-option\", {\n                            attrs: { label: \"50\", value: 50 },\n                          }),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n                  _vm._v(\" \"),\n                  _c(\"span\", { staticStyle: { \"margin-left\": \"20px\" } }, [\n                    _vm._v(\n                      \"\\n        \" +\n                        _vm._s(\n                          _vm.$t(\"affiliateProducts.totalCount\", {\n                            count: _vm.totalCount,\n                          })\n                        ) +\n                        \"\\n      \"\n                    ),\n                  ]),\n                ],\n                1\n              )\n            : _vm._e(),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-dialog\",\n        {\n          attrs: {\n            title: _vm.currentImportProduct\n              ? _vm.$t(\"affiliateProducts.importSingle\")\n              : _vm.$t(\"affiliateProducts.importBatch\"),\n            visible: _vm.importDialogVisible,\n            width: \"500px\",\n            \"close-on-click-modal\": false,\n          },\n          on: {\n            \"update:visible\": function ($event) {\n              _vm.importDialogVisible = $event\n            },\n          },\n        },\n        [\n          _vm.currentImportProduct\n            ? _c(\"div\", [\n                _c(\"p\", [\n                  _c(\"strong\", [\n                    _vm._v(\n                      _vm._s(_vm.$t(\"affiliateProducts.productTitle\")) + \"：\"\n                    ),\n                  ]),\n                  _vm._v(_vm._s(_vm.currentImportProduct.title)),\n                ]),\n                _vm._v(\" \"),\n                _c(\"p\", [\n                  _c(\"strong\", [\n                    _vm._v(_vm._s(_vm.$t(\"product.productId\")) + \"：\"),\n                  ]),\n                  _vm._v(_vm._s(_vm.currentImportProduct.id)),\n                ]),\n              ])\n            : _c(\"div\", [\n                _c(\"p\", [\n                  _c(\"strong\", [\n                    _vm._v(_vm._s(_vm.$t(\"affiliateProducts.selectedCount\"))),\n                  ]),\n                  _vm._v(_vm._s(_vm.selectedProducts.length)),\n                ]),\n              ]),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            {\n              staticStyle: {\n                margin: \"20px 0\",\n                padding: \"15px\",\n                \"background-color\": \"#f0f9ff\",\n                border: \"1px solid #b3d8ff\",\n                \"border-radius\": \"4px\",\n              },\n            },\n            [\n              _c(\"i\", {\n                staticClass: \"el-icon-info\",\n                staticStyle: { color: \"#409eff\", \"margin-right\": \"8px\" },\n              }),\n              _vm._v(\" \"),\n              _c(\"span\", { staticStyle: { color: \"#409eff\" } }, [\n                _vm._v(_vm._s(_vm.$t(\"affiliateProducts.brandAutoDetect\"))),\n              ]),\n            ]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            {\n              staticClass: \"dialog-footer\",\n              attrs: { slot: \"footer\" },\n              slot: \"footer\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  on: {\n                    click: function ($event) {\n                      _vm.importDialogVisible = false\n                    },\n                  },\n                },\n                [_vm._v(_vm._s(_vm.$t(\"affiliateProducts.cancel\")))]\n              ),\n              _vm._v(\" \"),\n              _c(\n                \"el-button\",\n                {\n                  attrs: { type: \"primary\" },\n                  on: { click: _vm.confirmImport },\n                },\n                [_vm._v(_vm._s(_vm.$t(\"affiliateProducts.confirmImport\")))]\n              ),\n            ],\n            1\n          ),\n        ]\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}