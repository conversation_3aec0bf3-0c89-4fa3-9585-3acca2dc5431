package com.genco.common.response;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 用户奖励统计数据
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "UserRewardStats", description = "用户奖励统计数据")
public class UserRewardStats implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "已兑换邀请人数")
    private Integer referralCount;

    @ApiModelProperty(value = "已兑换首单人数")
    private Integer firstOrderCount;
}
