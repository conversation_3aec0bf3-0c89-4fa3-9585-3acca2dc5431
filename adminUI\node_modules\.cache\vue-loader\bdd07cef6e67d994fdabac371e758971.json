{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\TagsView\\index.vue?vue&type=template&id=fac8ca64&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1754375668239}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return !_vm.isPhone\n    ? _c(\n        \"div\",\n        {\n          staticClass: \"tags-view-container\",\n          attrs: { id: \"tags-view-container\" },\n        },\n        [\n          _c(\n            \"scroll-pane\",\n            { ref: \"scrollPane\", staticClass: \"tags-view-wrapper\" },\n            _vm._l(_vm.visitedViews, function (tag) {\n              return _c(\n                \"router-link\",\n                {\n                  key: tag.path,\n                  staticClass: \"tags-view-item\",\n                  class: _vm.isActive(tag) ? \"active\" : \"\",\n                  attrs: {\n                    to: {\n                      path: tag.path,\n                      query: tag.query,\n                      fullPath: tag.fullPath,\n                    },\n                    tag: \"span\",\n                  },\n                  nativeOn: {\n                    mouseup: function ($event) {\n                      if (\"button\" in $event && $event.button !== 1) {\n                        return null\n                      }\n                      !_vm.isAffix(tag) ? _vm.closeSelectedTag(tag) : \"\"\n                    },\n                    contextmenu: function ($event) {\n                      $event.preventDefault()\n                      return _vm.openMenu(tag, $event)\n                    },\n                  },\n                },\n                [\n                  _vm._v(\n                    \"\\n      \" +\n                      _vm._s(_vm.$t(\"dashboard.\" + tag.title)) +\n                      \"\\n      \"\n                  ),\n                  !_vm.isAffix(tag)\n                    ? _c(\"span\", {\n                        staticClass: \"el-icon-close\",\n                        on: {\n                          click: function ($event) {\n                            $event.preventDefault()\n                            $event.stopPropagation()\n                            return _vm.closeSelectedTag(tag)\n                          },\n                        },\n                      })\n                    : _vm._e(),\n                ]\n              )\n            }),\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"ul\",\n            {\n              directives: [\n                {\n                  name: \"show\",\n                  rawName: \"v-show\",\n                  value: _vm.visible,\n                  expression: \"visible\",\n                },\n              ],\n              staticClass: \"contextmenu\",\n              style: { left: _vm.left + \"px\", top: _vm.top + \"px\" },\n            },\n            [\n              _c(\n                \"li\",\n                {\n                  on: {\n                    click: function ($event) {\n                      return _vm.refreshSelectedTag(_vm.selectedTag)\n                    },\n                  },\n                },\n                [\n                  _vm._v(\n                    \"\\n      \" + _vm._s(_vm.$t(\"tagsView.refresh\")) + \"\\n    \"\n                  ),\n                ]\n              ),\n              _vm._v(\" \"),\n              !_vm.isAffix(_vm.selectedTag)\n                ? _c(\n                    \"li\",\n                    {\n                      on: {\n                        click: function ($event) {\n                          return _vm.closeSelectedTag(_vm.selectedTag)\n                        },\n                      },\n                    },\n                    [\n                      _vm._v(\n                        \"\\n      \" + _vm._s(_vm.$t(\"tagsView.close\")) + \"\\n    \"\n                      ),\n                    ]\n                  )\n                : _vm._e(),\n              _vm._v(\" \"),\n              _c(\"li\", { on: { click: _vm.closeOthersTags } }, [\n                _vm._v(_vm._s(_vm.$t(\"tagsView.closeOthers\"))),\n              ]),\n              _vm._v(\" \"),\n              _c(\n                \"li\",\n                {\n                  on: {\n                    click: function ($event) {\n                      return _vm.closeAllTags(_vm.selectedTag)\n                    },\n                  },\n                },\n                [_vm._v(_vm._s(_vm.$t(\"tagsView.closeAll\")))]\n              ),\n            ]\n          ),\n        ],\n        1\n      )\n    : _vm._e()\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}