{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\Sidebar\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1754380939666}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport { mapGetters,mapState } from 'vuex'\r\nimport Logo from './Logo'\r\nimport SidebarItem from './SidebarItem'\r\nimport variables from '@/styles/variables.scss'\r\nexport default {\r\n  components: { SidebarItem, Logo },\r\n  computed: {\r\n    ...mapState([\"settings\"]),\r\n    ...mapGetters([\r\n      'permission_routes',\r\n      'sidebarRouters',\r\n      'sidebar'\r\n    ]),\r\n    activeMenu() {\r\n      const route = this.$route\r\n      const { meta, path } = route\r\n      // if set path, the sidebar will highlight the path you set\r\n      if (meta.activeMenu) {\r\n        return meta.activeMenu\r\n      }\r\n      return path\r\n    },\r\n    showLogo() {\r\n      return this.$store.state.settings.sidebarLogo\r\n    },\r\n    variables() {\r\n      return variables\r\n    },\r\n    isCollapse() {\r\n      return !this.sidebar.opened\r\n    }\r\n  },\r\n}\r\n", null]}