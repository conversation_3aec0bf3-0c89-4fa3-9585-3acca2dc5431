package com.genco.service.service;

import com.genco.common.request.UserExtractRequest;
import com.genco.common.response.WithdrawApplicationResult;
import com.genco.common.response.WithdrawEligibilityResult;
import com.genco.common.response.WithdrawLimitInfo;

/**
 * 提现申请管理器 - 核心业务接口
 * 
 * <AUTHOR>
 */
public interface WithdrawApplicationManager {
    
    /**
     * 提现申请
     * 
     * @param request 提现申请请求
     * @return 申请结果
     */
    WithdrawApplicationResult applyWithdraw(UserExtractRequest request);
    
    /**
     * 检查用户是否可以申请提现
     * 
     * @param userId 用户ID
     * @return 检查结果
     */
    WithdrawEligibilityResult checkWithdrawEligibility(Integer userId);
    
    /**
     * 获取用户提现限制信息
     * 
     * @param userId 用户ID
     * @return 限制信息
     */
    WithdrawLimitInfo getWithdrawLimitInfo(Integer userId);
}
