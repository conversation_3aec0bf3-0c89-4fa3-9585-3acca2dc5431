{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\router\\modules\\operation.js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\router\\modules\\operation.js", "mtime": 1754375668192}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _layout = _interopRequireDefault(require(\"@/layout\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\nfunction _typeof(o) { \"@babel/helpers - typeof\"; return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) { return typeof o; } : function (o) { return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o; }, _typeof(o); }\nfunction _interopRequireWildcard(e, t) { if (\"function\" == typeof WeakMap) var r = new WeakMap(), n = new WeakMap(); return (_interopRequireWildcard = function _interopRequireWildcard(e, t) { if (!t && e && e.__esModule) return e; var o, i, f = { __proto__: null, default: e }; if (null === e || \"object\" != _typeof(e) && \"function\" != typeof e) return f; if (o = t ? n : r) { if (o.has(e)) return o.get(e); o.set(e, f); } for (var _t in e) \"default\" !== _t && {}.hasOwnProperty.call(e, _t) && ((i = (o = Object.defineProperty) && Object.getOwnPropertyDescriptor(e, _t)) && (i.get || i.set) ? o(f, _t, i) : f[_t] = e[_t]); return f; })(e, t); } // +----------------------------------------------------------------------\n// | CRMEB [ CRMEB赋能开发者，助力企业发展 ]\n// +----------------------------------------------------------------------\n// | Copyright (c) 2016~2021 https://www.crmeb.com All rights reserved.\n// +----------------------------------------------------------------------\n// | Licensed CRMEB并不是自由软件，未经许可不能去掉CRMEB相关版权\n// +----------------------------------------------------------------------\n// | Author: CRMEB Team <<EMAIL>>\n// +----------------------------------------------------------------------\nvar operationRouter = {\n  path: '/operation',\n  component: _layout.default,\n  redirect: '/operation/setting',\n  name: 'Operation',\n  meta: {\n    title: 'accountCenter',\n    icon: 'clipboard',\n    roles: ['admin']\n  },\n  children: [{\n    path: 'setting',\n    name: 'setting',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/systemSetting/setting'));\n      });\n    },\n    meta: {\n      title: 'systemSettings',\n      icon: 'clipboard'\n    }\n  }, {\n    path: 'notification',\n    name: 'notification',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/systemSetting/notification'));\n      });\n    },\n    meta: {\n      title: '消息通知',\n      icon: 'clipboard'\n    }\n  }, {\n    path: 'onePass',\n    name: 'onePass',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/sms/smsConfig'));\n      });\n    },\n    meta: {\n      title: '一号通',\n      icon: 'clipboard'\n    }\n  }, {\n    path: 'roleManager',\n    name: 'RoleManager',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/systemSetting/administratorAuthority'));\n      });\n    },\n    meta: {\n      title: 'adminPermissions',\n      icon: 'clipboard',\n      roles: ['admin']\n    },\n    children: [{\n      path: 'identityManager',\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require('@/views/systemSetting/administratorAuthority/identityManager'));\n        });\n      },\n      name: 'identityManager',\n      meta: {\n        title: 'roleManage',\n        icon: ''\n      }\n    }, {\n      path: 'adminList',\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require('@/views/systemSetting/administratorAuthority/adminList'));\n        });\n      },\n      name: 'adminList',\n      meta: {\n        title: 'adminList',\n        icon: ''\n      }\n    }, {\n      path: 'promiseRules',\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require('@/views/systemSetting/administratorAuthority/permissionRules'));\n        });\n      },\n      name: 'promiseRules',\n      meta: {\n        title: 'permissionRules',\n        icon: ''\n      }\n    }]\n  }, {\n    path: 'systemSms',\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/sms'));\n      });\n    },\n    name: 'systemSms',\n    meta: {\n      title: '短信设置',\n      icon: 'clipboard',\n      roles: ['admin']\n    },\n    children: [{\n      path: 'config',\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require('@/views/sms/smsConfig'));\n        });\n      },\n      name: 'SmsConfig',\n      meta: {\n        title: '短信账户',\n        noCache: true\n      }\n    }, {\n      path: 'template',\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require('@/views/sms/smsTemplate'));\n        });\n      },\n      name: 'SmsTemplate',\n      meta: {\n        title: '短信模板',\n        noCache: true,\n        activeMenu: \"/operation/onePass\"\n      }\n    }, {\n      path: 'pay',\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require('@/views/sms/smsPay'));\n        });\n      },\n      name: 'SmsPay',\n      meta: {\n        title: '短信购买',\n        noCache: true,\n        activeMenu: \"/operation/onePass\"\n      }\n    }, {\n      path: 'message',\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require('@/views/sms/smsMessage'));\n        });\n      },\n      name: 'SmsMessage',\n      meta: {\n        title: '短信开关',\n        noCache: true\n      }\n    }]\n  }, {\n    path: 'deliverGoods',\n    name: 'deliverGoods',\n    alwaysShow: true,\n    component: function component() {\n      return Promise.resolve().then(function () {\n        return _interopRequireWildcard(require('@/views/systemSetting/deliverGoods'));\n      });\n    },\n    meta: {\n      title: '发货设置',\n      roles: ['admin']\n    },\n    children: [{\n      path: 'takeGoods',\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require('@/views/systemSetting/deliverGoods/takeGoods'));\n        });\n      },\n      name: 'takeGoods',\n      meta: {\n        title: '提货设置',\n        noCache: true,\n        roles: ['admin']\n      },\n      children: [{\n        path: 'deliveryAddress',\n        component: function component() {\n          return Promise.resolve().then(function () {\n            return _interopRequireWildcard(require('@/views/systemSetting/deliverGoods/takeGoods/deliveryAddress'));\n          });\n        },\n        name: 'deliveryAddress',\n        meta: {\n          title: '提货点',\n          icon: ''\n        }\n      }, {\n        path: 'collateOrder',\n        component: function component() {\n          return Promise.resolve().then(function () {\n            return _interopRequireWildcard(require('@/views/systemSetting/deliverGoods/takeGoods/collateOrder'));\n          });\n        },\n        name: 'collateOrder',\n        meta: {\n          title: '核销订单',\n          icon: ''\n        }\n      }, {\n        path: 'collateUser',\n        component: function component() {\n          return Promise.resolve().then(function () {\n            return _interopRequireWildcard(require('@/views/systemSetting/deliverGoods/takeGoods/collateUser'));\n          });\n        },\n        name: 'collateUser',\n        meta: {\n          title: '核销员',\n          icon: ''\n        }\n      }]\n    }, {\n      path: 'freightSet',\n      component: function component() {\n        return Promise.resolve().then(function () {\n          return _interopRequireWildcard(require('@/views/systemSetting/deliverGoods/freightSet'));\n        });\n      },\n      name: 'freightSet',\n      meta: {\n        title: '运费模板',\n        noCache: true\n      }\n    }]\n  }]\n};\nvar _default = exports.default = operationRouter; //collate", null]}