package com.genco.common.response;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 提现限制信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawLimitInfo {
    
    /**
     * 最小提现金额
     */
    private BigDecimal minAmount;
    
    /**
     * 最大提现金额
     */
    private BigDecimal maxAmount;
    
    /**
     * 手续费率
     */
    private BigDecimal feeRate;
    
    /**
     * 最小手续费
     */
    private BigDecimal minFee;
    
    /**
     * 每日提现次数限制
     */
    private Integer dailyLimit;
    
    /**
     * 今日已申请次数
     */
    private Integer todayApplications;
    
    /**
     * 冻结天数
     */
    private Integer freezeDays;
    
    /**
     * 工作日处理时间（小时）
     */
    private Integer workdayProcessHours;
    
    /**
     * 节假日处理时间（小时）
     */
    private Integer holidayProcessHours;
}
