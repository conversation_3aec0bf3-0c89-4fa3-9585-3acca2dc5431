{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\financial\\detail\\index.vue?vue&type=template&id=48cc36f4&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\financial\\detail\\index.vue", "mtime": 1754368747215}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    { staticClass: \"divBox relative\" },\n    [\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\" },\n        [\n          _c(\n            \"el-tabs\",\n            {\n              staticClass: \"mb20\",\n              on: {\n                \"tab-click\": function ($event) {\n                  return _vm.getList(_vm.tableFromType, 1)\n                },\n              },\n              model: {\n                value: _vm.tableFromType,\n                callback: function ($$v) {\n                  _vm.tableFromType = $$v\n                },\n                expression: \"tableFromType\",\n              },\n            },\n            [\n              _c(\"el-tab-pane\", {\n                attrs: {\n                  label: _vm.$t(\"financial.detail.purchaseDetail\"),\n                  name: \"purchase\",\n                },\n              }),\n              _vm._v(\" \"),\n              _c(\"el-tab-pane\", {\n                attrs: {\n                  label: _vm.$t(\"financial.detail.tradeDetail\"),\n                  name: \"trade\",\n                },\n              }),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"div\",\n            { staticClass: \"container mt-1\" },\n            [\n              _vm.tableFromType === \"purchase\"\n                ? _c(\n                    \"el-form\",\n                    {\n                      attrs: { inline: \"\", size: \"small\" },\n                      model: {\n                        value: _vm.purchaseFrom,\n                        callback: function ($$v) {\n                          _vm.purchaseFrom = $$v\n                        },\n                        expression: \"purchaseFrom\",\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        {\n                          attrs: {\n                            label:\n                              _vm.$t(\"financial.detail.rechargeType\") + \"：\",\n                          },\n                        },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              attrs: {\n                                placeholder: _vm.$t(\"common.all\"),\n                                clearable: \"\",\n                              },\n                              model: {\n                                value: _vm.purchaseFrom.rechargeType,\n                                callback: function ($$v) {\n                                  _vm.$set(\n                                    _vm.purchaseFrom,\n                                    \"rechargeType\",\n                                    $$v\n                                  )\n                                },\n                                expression: \"purchaseFrom.rechargeType\",\n                              },\n                            },\n                            _vm._l(\n                              _vm.rechargeTypeList,\n                              function (item, index) {\n                                return _c(\"el-option\", {\n                                  key: index,\n                                  attrs: {\n                                    label: _vm.$t(\n                                      \"financial.detail.\" + item.label\n                                    ),\n                                    value: item.value,\n                                  },\n                                })\n                              }\n                            ),\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        {\n                          attrs: {\n                            label:\n                              _vm.$t(\"financial.detail.transactionTime\") + \"：\",\n                          },\n                        },\n                        [\n                          _c(\"el-date-picker\", {\n                            staticStyle: { width: \"250px\" },\n                            attrs: {\n                              placeholder: _vm.$t(\"common.startDate\"),\n                              clearable: \"\",\n                              \"value-format\": \"yyyy-MM-dd\",\n                              format: \"yyyy-MM-dd\",\n                              size: \"small\",\n                              type: \"daterange\",\n                              placement: \"bottom-end\",\n                              \"start-placeholder\": _vm.$t(\"common.startDate\"),\n                              \"end-placeholder\": _vm.$t(\"common.endDate\"),\n                            },\n                            model: {\n                              value: _vm.timeList,\n                              callback: function ($$v) {\n                                _vm.timeList = $$v\n                              },\n                              expression: \"timeList\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        {\n                          attrs: {\n                            label:\n                              _vm.$t(\"financial.detail.paymentMethod\") + \"：\",\n                          },\n                        },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              attrs: {\n                                placeholder: _vm.$t(\"common.all\"),\n                                clearable: \"\",\n                              },\n                              model: {\n                                value: _vm.purchaseFrom.payChannel,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.purchaseFrom, \"payChannel\", $$v)\n                                },\n                                expression: \"purchaseFrom.payChannel\",\n                              },\n                            },\n                            [\n                              _c(\"el-option\", {\n                                attrs: {\n                                  label: _vm.$t(\n                                    \"financial.detail.bankTransfer\"\n                                  ),\n                                  value: \"xendit\",\n                                },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: {\n                                  label: _vm.$t(\n                                    \"financial.detail.electronicWallet\"\n                                  ),\n                                  value: \"haipay\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _vm.purchaseFrom.payChannel == \"haipay\"\n                        ? _c(\n                            \"el-form-item\",\n                            {\n                              attrs: {\n                                label:\n                                  _vm.$t(\"financial.detail.electronicWallet\") +\n                                  \"：\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-select\",\n                                {\n                                  attrs: {\n                                    placeholder: _vm.$t(\"common.all\"),\n                                    clearable: \"\",\n                                  },\n                                  model: {\n                                    value: _vm.purchaseFrom.walletCode,\n                                    callback: function ($$v) {\n                                      _vm.$set(\n                                        _vm.purchaseFrom,\n                                        \"walletCode\",\n                                        $$v\n                                      )\n                                    },\n                                    expression: \"purchaseFrom.walletCode\",\n                                  },\n                                },\n                                _vm._l(_vm.walletList, function (item) {\n                                  return _c(\"el-option\", {\n                                    key: item.value,\n                                    attrs: {\n                                      label: item.label,\n                                      value: item.value,\n                                    },\n                                  })\n                                }),\n                                1\n                              ),\n                            ],\n                            1\n                          )\n                        : _vm._e(),\n                      _vm._v(\" \"),\n                      _vm.purchaseFrom.payChannel == \"xendit\"\n                        ? _c(\n                            \"el-form-item\",\n                            {\n                              attrs: {\n                                label:\n                                  _vm.$t(\"financial.detail.bankName\") + \"：\",\n                              },\n                            },\n                            [\n                              _c(\n                                \"el-select\",\n                                {\n                                  attrs: {\n                                    clearable: \"\",\n                                    placeholder: _vm.$t(\"common.all\"),\n                                  },\n                                  model: {\n                                    value: _vm.purchaseFrom.bankName,\n                                    callback: function ($$v) {\n                                      _vm.$set(\n                                        _vm.purchaseFrom,\n                                        \"bankName\",\n                                        $$v\n                                      )\n                                    },\n                                    expression: \"purchaseFrom.bankName\",\n                                  },\n                                },\n                                _vm._l(_vm.bankList, function (item, index) {\n                                  return _c(\"el-option\", {\n                                    key: index,\n                                    attrs: { label: item, value: item },\n                                  })\n                                }),\n                                1\n                              ),\n                            ],\n                            1\n                          )\n                        : _vm._e(),\n                    ],\n                    1\n                  )\n                : _c(\n                    \"el-form\",\n                    {\n                      attrs: { inline: \"\", size: \"small\" },\n                      model: {\n                        value: _vm.tradeFrom,\n                        callback: function ($$v) {\n                          _vm.tradeFrom = $$v\n                        },\n                        expression: \"tradeFrom\",\n                      },\n                    },\n                    [\n                      _c(\n                        \"el-form-item\",\n                        {\n                          attrs: {\n                            label: _vm.$t(\"financial.detail.tradeNo\") + \"：\",\n                          },\n                        },\n                        [\n                          _c(\"el-input\", {\n                            attrs: {\n                              size: \"small\",\n                              placeholder: _vm.$t(\"financial.detail.tradeNo\"),\n                            },\n                            model: {\n                              value: _vm.tradeFrom.linkId,\n                              callback: function ($$v) {\n                                _vm.$set(_vm.tradeFrom, \"linkId\", $$v)\n                              },\n                              expression: \"tradeFrom.linkId\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        {\n                          attrs: {\n                            label:\n                              _vm.$t(\"financial.detail.transactionTime\") + \"：\",\n                          },\n                        },\n                        [\n                          _c(\"el-date-picker\", {\n                            staticStyle: { width: \"250px\" },\n                            attrs: {\n                              \"value-format\": \"yyyy-MM-dd\",\n                              format: \"yyyy-MM-dd\",\n                              size: \"small\",\n                              type: \"daterange\",\n                              placement: \"bottom-end\",\n                              \"start-placeholder\": _vm.$t(\"common.startDate\"),\n                              \"end-placeholder\": _vm.$t(\"common.endDate\"),\n                              clearable: \"\",\n                            },\n                            model: {\n                              value: _vm.timeList,\n                              callback: function ($$v) {\n                                _vm.timeList = $$v\n                              },\n                              expression: \"timeList\",\n                            },\n                          }),\n                        ],\n                        1\n                      ),\n                      _vm._v(\" \"),\n                      _c(\n                        \"el-form-item\",\n                        {\n                          attrs: {\n                            label: _vm.$t(\"financial.detail.tradeType\") + \"：\",\n                          },\n                        },\n                        [\n                          _c(\n                            \"el-select\",\n                            {\n                              attrs: {\n                                placeholder: _vm.$t(\"common.all\"),\n                                clearable: \"\",\n                              },\n                              model: {\n                                value: _vm.tradeFrom.type,\n                                callback: function ($$v) {\n                                  _vm.$set(_vm.tradeFrom, \"type\", $$v)\n                                },\n                                expression: \"tradeFrom.type\",\n                              },\n                            },\n                            [\n                              _c(\"el-option\", {\n                                attrs: {\n                                  label: _vm.$t(\"financial.detail.agentFee\"),\n                                  value: \"1\",\n                                },\n                              }),\n                              _vm._v(\" \"),\n                              _c(\"el-option\", {\n                                attrs: {\n                                  label: _vm.$t(\"financial.detail.partnerFee\"),\n                                  value: \"2\",\n                                },\n                              }),\n                            ],\n                            1\n                          ),\n                        ],\n                        1\n                      ),\n                    ],\n                    1\n                  ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              staticClass: \"mr10\",\n              attrs: { size: \"small\", type: \"primary\" },\n              on: {\n                click: function ($event) {\n                  return _vm.getList(_vm.tableFromType)\n                },\n              },\n            },\n            [_vm._v(_vm._s(_vm.$t(\"common.query\")))]\n          ),\n          _vm._v(\" \"),\n          _c(\n            \"el-button\",\n            {\n              staticClass: \"mr10\",\n              attrs: { size: \"small\", type: \"\" },\n              on: {\n                click: function ($event) {\n                  return _vm.handleReset()\n                },\n              },\n            },\n            [_vm._v(_vm._s(_vm.$t(\"common.reset\")))]\n          ),\n        ],\n        1\n      ),\n      _vm._v(\" \"),\n      _c(\n        \"el-card\",\n        { staticClass: \"box-card\", staticStyle: { \"margin-top\": \"12px\" } },\n        [\n          _c(\n            \"div\",\n            {\n              staticClass: \"clearfix\",\n              attrs: { slot: \"header\" },\n              slot: \"header\",\n            },\n            [\n              _c(\n                \"el-button\",\n                {\n                  directives: [\n                    {\n                      name: \"hasPermi\",\n                      rawName: \"v-hasPermi\",\n                      value: [\"admin:financialCenter:detail:upload\"],\n                      expression: \"['admin:financialCenter:detail:upload']\",\n                    },\n                  ],\n                  attrs: { type: \"primary\", size: \"small\" },\n                  on: { click: _vm.handleUpload },\n                },\n                [_vm._v(_vm._s(_vm.$t(\"financial.detail.exportExcel\")))]\n              ),\n            ],\n            1\n          ),\n          _vm._v(\" \"),\n          _vm.tableFromType === \"purchase\"\n            ? _c(\n                \"el-table\",\n                {\n                  directives: [\n                    {\n                      name: \"loading\",\n                      rawName: \"v-loading\",\n                      value: _vm.loading,\n                      expression: \"loading\",\n                    },\n                  ],\n                  attrs: {\n                    data: _vm.purchaseTableData,\n                    size: \"small\",\n                    \"header-cell-style\": { fontWeight: \"bold\" },\n                  },\n                },\n                [\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      type: \"index\",\n                      label: _vm.$t(\"common.serialNumber\"),\n                      width: \"110\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.paymentTime\"),\n                      \"min-width\": \"150\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(_vm._f(\"filterEmpty\")(scope.row.payTime))\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      1831590956\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.paymentNo\"),\n                      \"min-width\": \"150\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(\n                                  _vm._f(\"filterEmpty\")(scope.row.outTradeNo)\n                                )\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      **********\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.rechargeType\"),\n                      \"min-width\": \"80\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.paymentAccount\"),\n                      \"min-width\": \"80\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.tradeAmount\"),\n                      \"min-width\": \"100\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.actualPaymentAmount\"),\n                      \"min-width\": \"80\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.paymentMethod\"),\n                      \"min-width\": \"100\",\n                      prop: \"payChannel\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(\n                                  _vm._f(\"filterEmpty\")(scope.row.payChannel)\n                                )\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      3065867546\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.electronicWallet\"),\n                      \"min-width\": \"80\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.institutionNumber\"),\n                      \"min-width\": \"80\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.bankName\"),\n                      \"min-width\": \"80\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.paymentAccount\"),\n                      \"min-width\": \"80\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.mobile\"),\n                      \"min-width\": \"100\",\n                      prop: \"phone\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(_vm._f(\"filterEmpty\")(scope.row.phone))\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      **********\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.payee\"),\n                      \"min-width\": \"80\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.payeeAccount\"),\n                      \"min-width\": \"80\",\n                    },\n                  }),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.tableFromType === \"purchase\"\n            ? _c(\"el-pagination\", {\n                staticClass: \"mt20\",\n                attrs: {\n                  \"current-page\": _vm.purchaseFrom.page,\n                  \"page-sizes\": [20, 40, 60, 100],\n                  \"page-size\": _vm.purchaseFrom.limit,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.purchaseFrom.total,\n                },\n                on: {\n                  \"size-change\": function (e) {\n                    return _vm.sizeChange(e, \"purchase\")\n                  },\n                  \"current-change\": function (e) {\n                    return _vm.pageChange(e, \"purchase\")\n                  },\n                },\n              })\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.tableFromType === \"trade\"\n            ? _c(\n                \"el-table\",\n                {\n                  directives: [\n                    {\n                      name: \"loading\",\n                      rawName: \"v-loading\",\n                      value: _vm.loading,\n                      expression: \"loading\",\n                    },\n                  ],\n                  attrs: {\n                    data: _vm.tradeTableData,\n                    size: \"small\",\n                    \"header-cell-style\": { fontWeight: \"bold\" },\n                  },\n                },\n                [\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      type: \"index\",\n                      label: _vm.$t(\"common.serialNumber\"),\n                      width: \"110\",\n                    },\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.tradeNo\"),\n                      \"min-width\": \"150\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(_vm._f(\"filterEmpty\")(scope.row.linkId))\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      737300604\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.tradeType\"),\n                      \"min-width\": \"80\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(_vm._f(\"filterEmpty\")(scope.row.title))\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      3648579569\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.tradeAmount\"),\n                      \"min-width\": \"100\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(_vm._f(\"filterEmpty\")(scope.row.number))\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      2226103986\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.transactionTime\"),\n                      \"min-width\": \"150\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(\n                                  _vm._f(\"filterEmpty\")(scope.row.createTime)\n                                )\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      3756625312\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.userNickname\"),\n                      \"min-width\": \"80\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(\n                                  _vm._f(\"filterEmpty\")(scope.row.nickName)\n                                )\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      **********\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.tikTokAccount\"),\n                      \"min-width\": \"80\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(\n                                  _vm._f(\"filterEmpty\")(scope.row.tiktokAccount)\n                                )\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      **********\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.whatsApp\"),\n                      \"min-width\": \"80\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(\n                                  _vm._f(\"filterEmpty\")(\n                                    scope.row.whatsAppAccount\n                                  )\n                                )\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      **********\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.channel\"),\n                      \"min-width\": \"80\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(_vm._f(\"filterEmpty\")(scope.row.channel))\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      1225771826\n                    ),\n                  }),\n                  _vm._v(\" \"),\n                  _c(\"el-table-column\", {\n                    attrs: {\n                      label: _vm.$t(\"financial.detail.orderNo\"),\n                      \"min-width\": \"150\",\n                    },\n                    scopedSlots: _vm._u(\n                      [\n                        {\n                          key: \"default\",\n                          fn: function (scope) {\n                            return [\n                              _vm._v(\n                                _vm._s(_vm._f(\"filterEmpty\")(scope.row.linkId))\n                              ),\n                            ]\n                          },\n                        },\n                      ],\n                      null,\n                      false,\n                      737300604\n                    ),\n                  }),\n                ],\n                1\n              )\n            : _vm._e(),\n          _vm._v(\" \"),\n          _vm.tableFromType === \"trade\"\n            ? _c(\"el-pagination\", {\n                staticClass: \"mt20\",\n                attrs: {\n                  \"current-page\": _vm.tradeFrom.page,\n                  \"page-sizes\": [20, 40, 60, 100],\n                  \"page-size\": _vm.tradeFrom.limit,\n                  layout: \"total, sizes, prev, pager, next, jumper\",\n                  total: _vm.tradeFrom.total,\n                },\n                on: {\n                  \"size-change\": function (e) {\n                    return _vm.sizeChange(e, \"trade\")\n                  },\n                  \"current-change\": function (e) {\n                    return _vm.pageChange(e, \"trade\")\n                  },\n                },\n              })\n            : _vm._e(),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}