package com.genco.service.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.genco.common.model.user.UserTaskClaimRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户任务兑换记录表 Mapper 接口
 */
@Mapper
public interface UserTaskClaimRecordDao extends BaseMapper<UserTaskClaimRecord> {

    /**
     * 获取用户指定任务类型的累计兑换组数
     *
     * @param uid 用户ID
     * @param taskType 任务类型
     * @return 累计兑换组数
     */
    Integer getTotalClaimedGroups(@Param("uid") Integer uid, @Param("taskType") String taskType);

    /**
     * 获取用户指定任务类型已消耗的邀请人数
     *
     * @param uid 用户ID
     * @param taskType 任务类型
     * @return 已消耗邀请人数
     */
    Integer getTotalUsedReferralCount(@Param("uid") Integer uid, @Param("taskType") String taskType);

    /**
     * 获取用户指定任务类型已消耗的首单人数
     *
     * @param uid 用户ID
     * @param taskType 任务类型
     * @return 已消耗首单人数
     */
    Integer getTotalUsedFirstOrderCount(@Param("uid") Integer uid, @Param("taskType") String taskType);
}
