package com.genco.common.response;

import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 提现资格检查结果
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WithdrawEligibilityResult {
    
    /**
     * 是否有资格申请提现
     */
    private Boolean eligible;
    
    /**
     * 可提现金额
     */
    private BigDecimal availableAmount;
    
    /**
     * 冻结金额
     */
    private BigDecimal frozenAmount;
    
    /**
     * 是否有待审核的申请
     */
    private Boolean hasPendingApplication;
    
    /**
     * 待审核申请ID
     */
    private Integer pendingApplicationId;
    
    /**
     * 不符合条件的原因
     */
    private String ineligibleReason;
    
    /**
     * 下次可申请时间
     */
    private LocalDateTime nextAvailableTime;
    
    /**
     * 创建符合条件的结果
     */
    public static WithdrawEligibilityResult eligible(BigDecimal availableAmount, BigDecimal frozenAmount) {
        return WithdrawEligibilityResult.builder()
                .eligible(true)
                .availableAmount(availableAmount)
                .frozenAmount(frozenAmount)
                .hasPendingApplication(false)
                .build();
    }
    
    /**
     * 创建不符合条件的结果
     */
    public static WithdrawEligibilityResult ineligible(String reason) {
        return WithdrawEligibilityResult.builder()
                .eligible(false)
                .ineligibleReason(reason)
                .build();
    }
    
    /**
     * 创建有待审核申请的结果
     */
    public static WithdrawEligibilityResult hasPending(Integer pendingId) {
        return WithdrawEligibilityResult.builder()
                .eligible(false)
                .hasPendingApplication(true)
                .pendingApplicationId(pendingId)
                .ineligibleReason("您有待审核的提现申请，请等待处理完成")
                .build();
    }
}
