{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\Sidebar\\index.vue?vue&type=template&id=33ec43fc", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\Sidebar\\index.vue", "mtime": 1754380939666}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["var render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  return _c(\n    \"div\",\n    {\n      class: { \"has-logo\": _vm.showLogo },\n      style: { backgroundColor: _vm.variables.menuBackground },\n    },\n    [\n      _vm.showLogo\n        ? _c(\"logo\", { attrs: { collapse: _vm.isCollapse } })\n        : _vm._e(),\n      _vm._v(\" \"),\n      _c(\n        \"el-scrollbar\",\n        { attrs: { \"wrap-class\": \"scrollbar-wrapper\" } },\n        [\n          _c(\n            \"el-menu\",\n            {\n              attrs: {\n                \"default-active\": _vm.activeMenu,\n                collapse: _vm.isCollapse,\n                \"background-color\": _vm.variables.menuBackground,\n                \"text-color\": _vm.variables.menuColor,\n                \"unique-opened\": true,\n                \"active-text-color\": _vm.variables.menuActiveText,\n                \"collapse-transition\": true,\n                mode: \"vertical\",\n              },\n            },\n            _vm._l(_vm.sidebarRouters, function (route) {\n              return _c(\"sidebar-item\", {\n                key: route.url,\n                attrs: { item: route, \"base-path\": route.url },\n              })\n            }),\n            1\n          ),\n        ],\n        1\n      ),\n    ],\n    1\n  )\n}\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns }"]}