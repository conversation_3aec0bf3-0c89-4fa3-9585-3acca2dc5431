package com.genco.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.genco.common.constants.BrokerageRecordConstants;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.finance.UserExtract;
import com.genco.common.model.user.User;
import com.genco.common.model.user.UserBrokerageRecord;
import com.genco.common.request.UserExtractRequest;
import com.genco.common.response.*;
import com.genco.common.utils.DateUtil;
import com.genco.service.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 提现申请管理器实现
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class WithdrawApplicationManagerImpl implements WithdrawApplicationManager {
    
    @Autowired
    private UserService userService;
    
    @Autowired
    private UserExtractService userExtractService;
    
    @Autowired
    private UserBrokerageRecordService userBrokerageRecordService;
    
    @Autowired
    private SystemConfigService systemConfigService;
    
    @Autowired
    private TransactionTemplate transactionTemplate;
    
    // 用户级别的锁，防止同一用户并发申请
    private final ConcurrentHashMap<Integer, ReentrantLock> userLocks = new ConcurrentHashMap<>();
    
    @Override
    public WithdrawApplicationResult applyWithdraw(UserExtractRequest request) {
        Integer userId = userService.getUserIdException();
        
        // 获取用户锁
        ReentrantLock userLock = userLocks.computeIfAbsent(userId, k -> new ReentrantLock());
        
        try {
            userLock.lock();
            log.info("=== 提现申请开始 === 用户ID: {}, 申请金额: {}", userId, request.getExtractPrice());
            
            return processWithdrawApplication(userId, request);
            
        } finally {
            userLock.unlock();
            // 清理锁（可选，避免内存泄漏）
            if (!userLock.hasQueuedThreads()) {
                userLocks.remove(userId);
            }
            log.info("=== 提现申请结束 === 用户ID: {}", userId);
        }
    }
    
    /**
     * 处理提现申请的核心逻辑
     */
    private WithdrawApplicationResult processWithdrawApplication(Integer userId, UserExtractRequest request) {
        try {
            // 1. 资格检查
            WithdrawEligibilityResult eligibility = checkWithdrawEligibility(userId);
            if (!eligibility.getEligible()) {
                return WithdrawApplicationResult.failure("ELIGIBILITY_CHECK_FAILED", eligibility.getIneligibleReason());
            }
            
            // 2. 获取限制信息
            WithdrawLimitInfo limitInfo = getWithdrawLimitInfo(userId);
            
            // 3. 参数校验
            String validationError = validateWithdrawRequest(request, limitInfo, eligibility.getAvailableAmount());
            if (validationError != null) {
                return WithdrawApplicationResult.failure("VALIDATION_FAILED", validationError);
            }
            
            // 4. 计算手续费
            BigDecimal serviceFee = calculateServiceFee(request.getExtractPrice(), limitInfo);
            BigDecimal actualAmount = request.getExtractPrice().subtract(serviceFee);
            
            // 5. 执行提现申请（事务）
            return executeWithdrawApplication(userId, request, serviceFee, actualAmount);
            
        } catch (Exception e) {
            log.error("提现申请处理异常 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return WithdrawApplicationResult.failure("SYSTEM_ERROR", "系统繁忙，请稍后重试");
        }
    }
    
    /**
     * 执行提现申请（事务操作）
     */
    @Transactional(rollbackFor = Exception.class)
    public WithdrawApplicationResult executeWithdrawApplication(Integer userId, UserExtractRequest request, 
                                                               BigDecimal serviceFee, BigDecimal actualAmount) {
        
        return transactionTemplate.execute(status -> {
            try {
                log.info("开始执行提现申请事务 - 用户ID: {}", userId);
                
                // 1. 使用悲观锁锁定用户记录
                User user = userService.getUserForUpdate(userId);
                if (user == null) {
                    throw new CrmebException("用户不存在");
                }
                
                // 2. 再次检查余额（防止并发问题）
                if (user.getBrokeragePrice().compareTo(request.getExtractPrice()) < 0) {
                    throw new CrmebException("余额不足，当前可提现金额: " + user.getBrokeragePrice());
                }
                
                // 3. 扣减用户佣金余额
                BigDecimal newBalance = user.getBrokeragePrice().subtract(request.getExtractPrice());
                user.setBrokeragePrice(newBalance);
                boolean updateResult = userService.updateById(user);
                if (!updateResult) {
                    throw new CrmebException("余额更新失败");
                }
                
                log.info("用户佣金余额更新成功 - 用户ID: {}, 原余额: {}, 新余额: {}", 
                        userId, user.getBrokeragePrice().add(request.getExtractPrice()), newBalance);
                
                // 4. 创建提现记录
                UserExtract userExtract = createWithdrawRecord(user, request, serviceFee, actualAmount, newBalance);
                boolean saveResult = userExtractService.save(userExtract);
                if (!saveResult) {
                    throw new CrmebException("提现记录保存失败");
                }
                
                // 5. 创建佣金记录
                UserBrokerageRecord brokerageRecord = createBrokerageRecord(user, userExtract);
                boolean recordResult = userBrokerageRecordService.save(brokerageRecord);
                if (!recordResult) {
                    throw new CrmebException("佣金记录保存失败");
                }
                
                log.info("提现申请事务执行成功 - 用户ID: {}, 提现ID: {}", userId, userExtract.getId());
                
                return WithdrawApplicationResult.success(
                    userExtract.getId(),
                    request.getExtractPrice(),
                    serviceFee,
                    actualAmount,
                    newBalance
                );
                
            } catch (Exception e) {
                log.error("提现申请事务执行失败 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
                status.setRollbackOnly();
                throw new CrmebException("提现申请失败: " + e.getMessage());
            }
        });
    }

    @Override
    public WithdrawEligibilityResult checkWithdrawEligibility(Integer userId) {
        try {
            User user = userService.getById(userId);
            if (user == null) {
                return WithdrawEligibilityResult.ineligible("用户不存在");
            }

            // 检查是否有待审核的申请
            List<UserExtract> pendingApplications = userExtractService.list(
                new QueryWrapper<UserExtract>()
                    .eq("uid", userId)
                    .eq("status", 0) // 审核中
            );

            if (!pendingApplications.isEmpty()) {
                return WithdrawEligibilityResult.hasPending(pendingApplications.get(0).getId());
            }

            // 获取可提现金额和冻结金额
            BigDecimal availableAmount = user.getBrokeragePrice();
            BigDecimal frozenAmount = userBrokerageRecordService.getFreezePrice(userId);

            if (availableAmount.compareTo(BigDecimal.ZERO) <= 0) {
                return WithdrawEligibilityResult.ineligible("可提现金额不足");
            }

            return WithdrawEligibilityResult.eligible(availableAmount, frozenAmount);

        } catch (Exception e) {
            log.error("检查提现资格异常 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            return WithdrawEligibilityResult.ineligible("系统异常，请稍后重试");
        }
    }

    @Override
    public WithdrawLimitInfo getWithdrawLimitInfo(Integer userId) {
        try {
            // 从系统配置获取限制信息，使用安全的方式获取配置
            String minAmountStr = systemConfigService.getValueByKey("min_withdraw_amount");
            BigDecimal minAmount = new BigDecimal(minAmountStr.isEmpty() ? "100" : minAmountStr);

            String maxAmountStr = systemConfigService.getValueByKey("max_withdraw_amount");
            BigDecimal maxAmount = new BigDecimal(maxAmountStr.isEmpty() ? "50000" : maxAmountStr);

            String feeRateStr = systemConfigService.getValueByKey("withdraw_fee_rate");
            BigDecimal feeRate = new BigDecimal(feeRateStr.isEmpty() ? "0.015" : feeRateStr);

            String minFeeStr = systemConfigService.getValueByKey("min_withdraw_fee");
            BigDecimal minFee = new BigDecimal(minFeeStr.isEmpty() ? "5" : minFeeStr);

            String dailyLimitStr = systemConfigService.getValueByKey("daily_withdraw_limit");
            Integer dailyLimit = Integer.valueOf(dailyLimitStr.isEmpty() ? "3" : dailyLimitStr);

            String freezeDaysStr = systemConfigService.getValueByKey("withdraw_freeze_days");
            Integer freezeDays = Integer.valueOf(freezeDaysStr.isEmpty() ? "7" : freezeDaysStr);

            // 获取今日申请次数
            Integer todayApplications = getTodayApplicationCount(userId);

            log.info("获取提现限制信息成功 - 用户ID: {}, 最小金额: {}, 最大金额: {}, 手续费率: {}",
                    userId, minAmount, maxAmount, feeRate);

            return WithdrawLimitInfo.builder()
                .minAmount(minAmount)
                .maxAmount(maxAmount)
                .feeRate(feeRate)
                .minFee(minFee)
                .dailyLimit(dailyLimit)
                .todayApplications(todayApplications)
                .freezeDays(freezeDays)
                .workdayProcessHours(24)
                .holidayProcessHours(72)
                .build();

        } catch (Exception e) {
            log.error("获取提现限制信息异常 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            // 返回默认值
            return WithdrawLimitInfo.builder()
                .minAmount(new BigDecimal("100"))
                .maxAmount(new BigDecimal("50000"))
                .feeRate(new BigDecimal("0.015"))
                .minFee(new BigDecimal("5"))
                .dailyLimit(3)
                .todayApplications(0)
                .freezeDays(7)
                .workdayProcessHours(24)
                .holidayProcessHours(72)
                .build();
        }
    }

    /**
     * 校验提现请求参数
     */
    private String validateWithdrawRequest(UserExtractRequest request, WithdrawLimitInfo limitInfo, BigDecimal availableAmount) {
        // 金额校验
        if (request.getExtractPrice().compareTo(limitInfo.getMinAmount()) < 0) {
            return "提现金额不能小于 " + limitInfo.getMinAmount();
        }

        if (request.getExtractPrice().compareTo(limitInfo.getMaxAmount()) > 0) {
            return "提现金额不能大于 " + limitInfo.getMaxAmount();
        }

        if (request.getExtractPrice().compareTo(availableAmount) > 0) {
            return "提现金额不能大于可提现余额 " + availableAmount;
        }

        // 频率校验
        if (limitInfo.getTodayApplications() >= limitInfo.getDailyLimit()) {
            return "今日提现次数已达上限 " + limitInfo.getDailyLimit() + " 次";
        }

        // 提现方式校验
        if (!"bank".equals(request.getExtractType()) && !"wallet".equals(request.getExtractType())) {
            return "请选择正确的提现方式";
        }

        // 银行卡信息校验
        if ("bank".equals(request.getExtractType())) {
            if (request.getBankName() == null || request.getBankName().trim().isEmpty()) {
                return "请填写银行名称";
            }
            if (request.getBankCode() == null || request.getBankCode().trim().isEmpty()) {
                return "请填写银行卡号";
            }
        }

        // 电子钱包信息校验
        if ("wallet".equals(request.getExtractType())) {
            if (request.getWalletCode() == null || request.getWalletCode().trim().isEmpty()) {
                return "请选择电子钱包类型";
            }
            if (request.getWalletAccount() == null || request.getWalletAccount().trim().isEmpty()) {
                return "请填写电子钱包账号";
            }
        }

        return null; // 校验通过
    }

    /**
     * 计算手续费
     */
    private BigDecimal calculateServiceFee(BigDecimal amount, WithdrawLimitInfo limitInfo) {
        BigDecimal fee = amount.multiply(limitInfo.getFeeRate()).setScale(2, BigDecimal.ROUND_HALF_UP);
        // 确保手续费不低于最小手续费
        return fee.max(limitInfo.getMinFee());
    }

    /**
     * 获取今日申请次数
     */
    private Integer getTodayApplicationCount(Integer userId) {
        LocalDate today = LocalDate.now();
        String todayStr = today.toString();

        return userExtractService.count(
            new QueryWrapper<UserExtract>()
                .eq("uid", userId)
                .apply("DATE(create_time) = {0}", todayStr)
        );
    }

    /**
     * 创建提现记录
     */
    private UserExtract createWithdrawRecord(User user, UserExtractRequest request,
                                           BigDecimal serviceFee, BigDecimal actualAmount, BigDecimal balance) {
        UserExtract userExtract = new UserExtract();
        userExtract.setUid(user.getUid());
        userExtract.setRealName(request.getRealName());
        userExtract.setExtractType(request.getExtractType());
        userExtract.setExtractPrice(request.getExtractPrice());
        userExtract.setServiceFee(serviceFee);
        userExtract.setActualAmount(actualAmount);
        userExtract.setBalance(balance);
        userExtract.setMark(request.getMark());
        userExtract.setStatus(0); // 待审核
        userExtract.setCreateTime(DateUtil.nowDateTime());

        // 设置提现方式相关信息
        if ("bank".equals(request.getExtractType())) {
            userExtract.setBankName(request.getBankName());
            userExtract.setBankCode(request.getBankCode());
            userExtract.setWalletCode(null);
            userExtract.setWalletAccount(null);
        } else if ("wallet".equals(request.getExtractType())) {
            userExtract.setWalletCode(request.getWalletCode());
            userExtract.setWalletAccount(request.getWalletAccount());
            userExtract.setBankName(null);
            userExtract.setBankCode(null);
        }

        return userExtract;
    }

    /**
     * 创建佣金记录
     */
    private UserBrokerageRecord createBrokerageRecord(User user, UserExtract userExtract) {
        UserBrokerageRecord record = new UserBrokerageRecord();
        record.setUid(user.getUid());
        record.setLinkId(userExtract.getId().toString());
        record.setLinkType(BrokerageRecordConstants.BROKERAGE_RECORD_LINK_TYPE_WITHDRAW);
        record.setType(BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_SUB);
        record.setTitle(BrokerageRecordConstants.BROKERAGE_RECORD_TITLE_WITHDRAW_APPLY);
        record.setPrice(userExtract.getExtractPrice());
        record.setBalance(userExtract.getBalance());
        record.setMark(String.format("提现申请扣除佣金 %.2f 元", userExtract.getExtractPrice()));
        record.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_WITHDRAW);
        record.setCreateTime(DateUtil.nowDateTime());

        return record;
    }
}
