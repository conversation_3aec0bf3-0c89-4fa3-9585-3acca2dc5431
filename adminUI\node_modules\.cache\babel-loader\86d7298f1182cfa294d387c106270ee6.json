{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\components\\Breadcrumb\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\components\\Breadcrumb\\index.vue", "mtime": 1754381016270}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\babel.config.js", "mtime": 1754050582109}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.default = void 0;\nvar _pathToRegexp = _interopRequireDefault(require(\"path-to-regexp\"));\nfunction _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\nvar _default = exports.default = {\n  data: function data() {\n    return {\n      levelList: null\n    };\n  },\n  watch: {\n    $route: function $route(route) {\n      // if you go to the redirect page, do not update the breadcrumbs\n      if (route.path.startsWith('/redirect/')) {\n        return;\n      }\n      this.getBreadcrumb();\n    }\n  },\n  created: function created() {\n    this.getBreadcrumb();\n  },\n  methods: {\n    getBreadcrumb: function getBreadcrumb() {\n      // only show routes with meta.title\n      var matched = this.$route.matched.filter(function (item) {\n        return item.meta && item.meta.title;\n      });\n      var first = matched[0];\n\n      // if (!this.isDashboard(first)) {\n      //   matched = [{ path: '/homepage', meta: { title: 'home' }}].concat(matched)\n      // }\n\n      this.levelList = matched.filter(function (item) {\n        return item.meta && item.meta.title && item.meta.breadcrumb !== false;\n      });\n      console.log(this.levelList);\n    },\n    isDashboard: function isDashboard(route) {\n      var name = route && route.name;\n      if (!name) {\n        return false;\n      }\n      return name.trim().toLocaleLowerCase() === 'Homepage'.toLocaleLowerCase();\n    },\n    pathCompile: function pathCompile(path) {\n      // To solve this problem https://github.com/PanJiaChen/vue-element-admin/issues/561\n      var params = this.$route.params;\n      var toPath = _pathToRegexp.default.compile(path);\n      return toPath(params);\n    },\n    handleLink: function handleLink(item) {\n      var redirect = item.redirect,\n        path = item.path;\n      if (redirect) {\n        this.$router.push(redirect);\n        return;\n      }\n      this.$router.push(this.pathCompile(path));\n    }\n  }\n};", null]}