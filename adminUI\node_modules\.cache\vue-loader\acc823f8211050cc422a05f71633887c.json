{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\permissionRules\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\systemSetting\\administratorAuthority\\permissionRules\\index.vue", "mtime": 1754373878576}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1754052678844}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n//\n\r\nimport {\r\n  menuListApi,\r\n  menuInfo,\r\n  menuUpdate,\r\n  menuAdd,\r\n  menuDelete\r\n} from \"@/api/systemadmin\";\r\nimport Treeselect from \"@riophae/vue-treeselect\";\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\";\r\nimport { Debounce } from \"@/utils/validate\";\r\n\r\nexport default {\r\n  name: \"Menu\",\r\n  components: { Treeselect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      listLoading: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 菜单表格树数据\r\n      menuList: [],\r\n      // 菜单树选项\r\n      menuOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否展开，默认全部折叠\r\n      isExpandAll: false,\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 查询参数\r\n      queryParams: {\r\n        name: \"\",\r\n        menuType: \"\"\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 请求到的menu数据\r\n      menuDataList: [],\r\n      // 表单校验\r\n      rules: {\r\n        name: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"permissionRules.form.enterMenuName\"),\r\n            trigger: \"blur\"\r\n          }\r\n        ],\r\n        sort: [\r\n          {\r\n            required: true,\r\n            message: this.$t(\"permissionRules.form.sortRequired\"),\r\n            trigger: \"blur\"\r\n          }\r\n        ]\r\n      },\r\n      statusOptions: [\r\n        { value: \"M\", label: this.$t(\"permissionRules.menuType.directory\") },\r\n        { value: \"C\", label: this.$t(\"permissionRules.menuType.menu\") },\r\n        { value: \"A\", label: this.$t(\"permissionRules.menuType.button\") }\r\n      ],\r\n      showStatus: [\r\n        { label: this.$t(\"common.show\"), value: true },\r\n        { label: this.$t(\"common.hide\"), value: false }\r\n      ]\r\n    };\r\n  },\r\n  created() {\r\n    this.getList();\r\n  },\r\n  methods: {\r\n    // 点击图标\r\n    addIcon() {\r\n      const _this = this;\r\n      _this.$modalIcon(function(icon) {\r\n        _this.form.icon = icon;\r\n      });\r\n    },\r\n\r\n    /** 查询菜单列表 */\r\n    getList() {\r\n      this.listLoading = true;\r\n      menuListApi(this.queryParams).then(res => {\r\n        let obj = {},\r\n          menuList = [];\r\n        res.forEach(item => {\r\n          obj = item;\r\n          obj.parentId = item.pid;\r\n          obj.children = [];\r\n          menuList.push(obj);\r\n        });\r\n        this.menuDataList = menuList;\r\n        this.menuList = this.handleTree(menuList, \"menuId\");\r\n        this.listLoading = false;\r\n      });\r\n    },\r\n\r\n    /** 转换菜单数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children;\r\n      }\r\n      return {\r\n        id: node.id ? node.id : 0,\r\n        label: node.name ? node.name : \"主目录\",\r\n        children: node.children\r\n      };\r\n    },\r\n\r\n    /** 查询菜单下拉树结构 */\r\n    getTreeselect() {\r\n      this.menuOptions = [];\r\n      const menu = { menuId: 0, menuName: \"主类目\", children: [] };\r\n      menu.children = this.handleTree(this.menuDataList, \"menuId\");\r\n      this.menuOptions.push(menu);\r\n    },\r\n\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false;\r\n      this.reset();\r\n    },\r\n\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        menuId: \"\",\r\n        parentId: 0,\r\n        name: \"\",\r\n        icon: \"\",\r\n        menuType: \"M\",\r\n        sort: 0,\r\n        isShow: true,\r\n        component: \"\",\r\n        perms: \"\"\r\n      };\r\n      this.resetForm(\"form\");\r\n    },\r\n\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.getList();\r\n    },\r\n\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.queryParams = { name: \"\", menuType: \"\" };\r\n      this.handleQuery();\r\n    },\r\n\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset();\r\n      if (row != null && row.id) {\r\n        this.form.pid = row.id;\r\n      } else {\r\n        this.form.pid = 0;\r\n      }\r\n      this.open = true;\r\n      this.title = this.$t(\"permissionRules.actions.add\");\r\n    },\r\n\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false;\r\n      this.isExpandAll = !this.isExpandAll;\r\n      this.$nextTick(() => {\r\n        this.refreshTable = true;\r\n      });\r\n    },\r\n\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      const loading = this.$loading({\r\n        lock: true,\r\n        text: \"Loading\"\r\n      });\r\n      this.reset();\r\n      this.getTreeselect();\r\n      menuInfo(row.id).then(response => {\r\n        this.form = response;\r\n        this.open = true;\r\n        this.title = this.$t(\"permissionRules.actions.edit\");\r\n        loading.close();\r\n      });\r\n    },\r\n\r\n    /** 提交按钮 */\r\n    submitForm: Debounce(function() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != undefined) {\r\n            menuUpdate(this.form).then(response => {\r\n              this.$modal.msgSuccess(this.$t(\"common.editSuccess\"));\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          } else {\r\n            menuAdd(this.form).then(response => {\r\n              this.$modal.msgSuccess(this.$t(\"common.addSuccess\"));\r\n              this.open = false;\r\n              this.getList();\r\n            });\r\n          }\r\n        }\r\n      });\r\n    }),\r\n\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      this.$modal\r\n        .confirm(this.$t(\"common.confirmDelete\").replace(\"{name}\", row.name))\r\n        .then(() => {\r\n          return menuDelete(row.id);\r\n        })\r\n        .then(() => {\r\n          this.getList();\r\n          this.$modal.msgSuccess(this.$t(\"common.deleteSuccess\"));\r\n        })\r\n        .catch(() => {});\r\n    }\r\n  }\r\n};\r\n", null]}