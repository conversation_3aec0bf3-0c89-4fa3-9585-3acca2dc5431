package com.genco.service.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.common.model.user.UserTaskClaimRecord;
import com.genco.common.response.UserRewardStats;
import com.genco.service.dao.UserTaskClaimRecordDao;
import com.genco.service.service.UserTaskClaimRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 用户任务兑换记录表 服务实现类
 */
@Slf4j
@Service
public class UserTaskClaimRecordServiceImpl extends ServiceImpl<UserTaskClaimRecordDao, UserTaskClaimRecord> implements UserTaskClaimRecordService {

    /**
     * 获取用户指定任务类型的奖励统计数据
     *
     * @param uid 用户ID
     * @param taskType 任务类型
     * @return 奖励统计数据
     */
    @Override
    public UserRewardStats getRedeemedStats(Integer uid, String taskType) {
        if (uid == null || taskType == null) {
            return new UserRewardStats(0, 0);
        }

        try {
            Integer totalUsedReferralCount = baseMapper.getTotalUsedReferralCount(uid, taskType);
            Integer totalUsedFirstOrderCount = baseMapper.getTotalUsedFirstOrderCount(uid, taskType);

            return new UserRewardStats(
                totalUsedReferralCount != null ? totalUsedReferralCount : 0,
                totalUsedFirstOrderCount != null ? totalUsedFirstOrderCount : 0
            );
        } catch (Exception e) {
            log.error("获取用户{}任务{}奖励统计数据失败", uid, taskType, e);
            return new UserRewardStats(0, 0);
        }
    }

    /**
     * 获取用户指定任务类型的累计兑换组数
     *
     * @param uid 用户ID
     * @param taskType 任务类型
     * @return 累计兑换组数
     */
    @Override
    public Integer getTotalClaimedGroups(Integer uid, String taskType) {
        if (uid == null || taskType == null) {
            return 0;
        }

        try {
            Integer totalGroups = baseMapper.getTotalClaimedGroups(uid, taskType);
            return totalGroups != null ? totalGroups : 0;
        } catch (Exception e) {
            log.error("获取用户{}任务{}累计兑换组数失败", uid, taskType, e);
            return 0;
        }
    }
}
