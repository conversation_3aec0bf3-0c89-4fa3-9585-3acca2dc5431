package com.genco.service.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.genco.common.model.user.UserReferralRewardConfig;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户拉新奖励配置表 Mapper 接口
 */
@Mapper
public interface UserReferralRewardConfigDao extends BaseMapper<UserReferralRewardConfig> {

    /**
     * 根据用户ID查询拉新奖励配置
     *
     * @param uid 用户ID
     * @return UserReferralRewardConfig
     */
    UserReferralRewardConfig selectByUid(@Param("uid") Integer uid);

    /**
     * 查询用户的有效拉新奖励配置（优先级：银级会员配置 > 平台配置）
     *
     * @param uid 用户ID
     * @return UserReferralRewardConfig
     */
    UserReferralRewardConfig selectEffectiveConfigByUid(@Param("uid") Integer uid);

    /**
     * 获取指定推广人的下级用户首单总数
     *
     * @param spreadUid 推广人ID
     * @return 首单总数
     */
    Integer getTotalFirstOrderCountBySpreadUid(@Param("spreadUid") Integer spreadUid);
}
