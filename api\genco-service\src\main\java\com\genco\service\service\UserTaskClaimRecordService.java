package com.genco.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.genco.common.model.user.UserTaskClaimRecord;
import com.genco.common.response.UserRewardStats;

/**
 * 用户任务兑换记录表 服务类
 */
public interface UserTaskClaimRecordService extends IService<UserTaskClaimRecord> {

    /**
     * 获取用户指定任务类型的奖励统计数据
     *
     * @param uid 用户ID
     * @param taskType 任务类型
     * @return 奖励统计数据
     */
    UserRewardStats getRedeemedStats(Integer uid, String taskType);

    /**
     * 获取用户指定任务类型的累计兑换组数
     *
     * @param uid 用户ID
     * @param taskType 任务类型
     * @return 累计兑换组数
     */
    Integer getTotalClaimedGroups(Integer uid, String taskType);
}
