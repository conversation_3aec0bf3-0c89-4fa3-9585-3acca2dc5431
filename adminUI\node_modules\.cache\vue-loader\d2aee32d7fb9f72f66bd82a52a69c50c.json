{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\affiliate-products\\index.vue?vue&type=template&id=76995300&scoped=true", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\views\\operations\\affiliate-products\\index.vue", "mtime": 1754374966345}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 1754052682065}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}], "contextDependencies": [], "result": ["\n<div class=\"divBox relative\">\n  <!-- 搜索表单 -->\n  <el-card class=\"box-card\">\n    <el-form :model=\"searchForm\" :rules=\"searchRules\" ref=\"searchFormRef\" inline size=\"small\">\n\n\n      <el-form-item :label=\"$t('affiliateProducts.priceRange')\">\n        <el-input\n          v-model=\"searchForm.priceMin\"\n          :placeholder=\"$t('affiliateProducts.minPrice')\"\n          style=\"width: 120px;\"\n        ></el-input>\n        <span style=\"margin: 0 8px;\">-</span>\n        <el-input\n          v-model=\"searchForm.priceMax\"\n          :placeholder=\"$t('affiliateProducts.maxPrice')\"\n          style=\"width: 120px;\"\n        ></el-input>\n      </el-form-item>\n\n      <el-form-item :label=\"$t('affiliateProducts.commissionRange')\" prop=\"commissionRange\">\n        <el-input\n          v-model=\"searchForm.commissionMin\"\n          :placeholder=\"$t('affiliateProducts.minCommission')\"\n          style=\"width: 120px;\"\n          @blur=\"validateCommissionRate('commissionMin')\"\n          @input=\"clearCommissionError('commissionMin')\"\n        ></el-input>\n        <span style=\"margin: 0 8px;\">-</span>\n        <el-input\n          v-model=\"searchForm.commissionMax\"\n          :placeholder=\"$t('affiliateProducts.maxCommission')\"\n          style=\"width: 120px;\"\n          @blur=\"validateCommissionRate('commissionMax')\"\n          @input=\"clearCommissionError('commissionMax')\"\n        ></el-input>\n        <div v-if=\"commissionErrors.commissionMin\" class=\"commission-error\">\n          {{ commissionErrors.commissionMin }}\n        </div>\n        <div v-if=\"commissionErrors.commissionMax\" class=\"commission-error\">\n          {{ commissionErrors.commissionMax }}\n        </div>\n      </el-form-item>\n\n\n\n      <el-form-item :label=\"$t('affiliateProducts.sort')\">\n        <el-select v-model=\"searchForm.sortField\" style=\"width: 140px;\">\n          <el-option :label=\"$t('affiliateProducts.sortCommissionRate')\" value=\"commission_rate\"></el-option>\n          <el-option :label=\"$t('affiliateProducts.sortCommission')\" value=\"commission\"></el-option>\n          <el-option :label=\"$t('affiliateProducts.sortPrice')\" value=\"product_sales_price\"></el-option>\n          <el-option :label=\"$t('affiliateProducts.sortSales')\" value=\"units_sold\"></el-option>\n        </el-select>\n        <el-select v-model=\"searchForm.sortOrder\" style=\"width: 80px; margin-left: 8px;\">\n          <el-option :label=\"$t('affiliateProducts.sortDesc')\" value=\"DESC\"></el-option>\n          <el-option :label=\"$t('affiliateProducts.sortAsc')\" value=\"ASC\"></el-option>\n        </el-select>\n      </el-form-item>\n\n      <el-form-item :label=\"$t('affiliateProducts.keywords')\">\n        <el-input\n          v-model=\"currentKeyword\"\n          :placeholder=\"$t('affiliateProducts.addKeywordPlaceholder')\"\n          @keyup.enter.native=\"addKeyword\"\n          style=\"width: 300px;\"\n          clearable\n          :disabled=\"searchForm.keywords.length >= 20\"\n        >\n          <template slot=\"append\">\n            <span class=\"keyword-count\" :class=\"{ 'keyword-count-warning': searchForm.keywords.length >= 18 }\">\n              {{ searchForm.keywords.length }}/20\n            </span>\n          </template>\n        </el-input>\n        <div v-if=\"keywordError\" class=\"keyword-error\" style=\"margin-top: 4px;\">\n          {{ keywordError }}\n        </div>\n      </el-form-item>\n\n    </el-form>\n\n    <!-- 操作按钮区域 -->\n    <div class=\"search-actions\">\n      <el-button type=\"primary\" @click=\"handleSearch\">{{ $t('affiliateProducts.query') }}</el-button>\n      <el-button @click=\"handleReset\">{{ $t('affiliateProducts.reset') }}</el-button>\n    </div>\n\n    <!-- 关键词展示区域 -->\n    <div class=\"keywords-display-section\" v-if=\"searchForm.keywords.length > 0\" style=\"margin-top: 16px;\">\n      <div class=\"keywords-display-header\">\n        <div class=\"keywords-display-label\">{{ $t('affiliateProducts.selectedKeywords') }}</div>\n        <el-button\n          size=\"mini\"\n          type=\"text\"\n          icon=\"el-icon-delete\"\n          @click=\"clearAllKeywords\"\n          class=\"clear-all-btn\"\n          :title=\"$t('affiliateProducts.clearAllKeywords')\"\n        >\n          {{ $t('affiliateProducts.clearAll') }}\n        </el-button>\n      </div>\n      <div class=\"keywords-display-container\">\n        <el-tag\n          v-for=\"(keyword, index) in searchForm.keywords\"\n          :key=\"index\"\n          closable\n          @close=\"removeKeyword(index)\"\n          class=\"keyword-display-tag\"\n        >\n          {{ keyword }}\n        </el-tag>\n      </div>\n    </div>\n  </el-card>\n\n  <!-- 产品列表 -->\n  <el-card class=\"box-card\" style=\"margin-top: 12px;\">\n    <div slot=\"header\" class=\"clearfix\">\n      <span>{{ $t('affiliateProducts.listTitle') }}</span>\n      <el-button\n        style=\"float: right; padding: 3px 0\"\n        type=\"text\"\n        @click=\"handleRefresh\"\n      >{{ $t('affiliateProducts.refresh') }}</el-button>\n    </div>\n\n    <!-- 批量操作按钮 -->\n    <div v-if=\"hasSearched && tableData.length > 0\" style=\"margin-bottom: 15px;\">\n      <el-button\n        type=\"primary\"\n        size=\"small\"\n        @click=\"handleBatchImport\"\n        :disabled=\"selectedProducts.length === 0 || batchImporting\"\n      >\n        {{ batchImporting ? $t('affiliateProducts.batchImporting') : `${$t('affiliateProducts.batchImport')} (${selectedProducts.length})` }}\n      </el-button>\n      <el-button\n        type=\"danger\"\n        size=\"small\"\n        @click=\"handleBatchDelete\"\n        :disabled=\"selectedProducts.length === 0 || batchDeleting\"\n      >\n        {{ batchDeleting ? $t('affiliateProducts.batchDeleting') : `${$t('affiliateProducts.batchDelete')} (${selectedProducts.length})` }}\n      </el-button>\n    </div>\n\n    <!-- 未搜索时的提示 -->\n    <div v-if=\"!hasSearched && tableData.length === 0\" class=\"empty-tip\">\n      <el-empty :description=\"$t('affiliateProducts.emptyTip')\"></el-empty>\n    </div>\n\n    <el-table\n      ref=\"productTable\"\n      v-loading=\"loading\"\n      :data=\"tableData\"\n      size=\"small\"\n      :header-cell-style=\"{ fontWeight: 'bold' }\"\n      v-show=\"hasSearched\"\n      @selection-change=\"handleSelectionChange\"\n    >\n      <el-table-column type=\"selection\" width=\"55\"></el-table-column>\n      <el-table-column type=\"index\" :label=\"$t('affiliateProducts.serialNumber')\" width=\"60\"></el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.productImage')\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <el-image\n            :src=\"scope.row.mainImageUrl\"\n            fit=\"cover\"\n            style=\"width: 60px; height: 60px; border-radius: 4px;\"\n            :preview-src-list=\"[scope.row.mainImageUrl]\"\n          >\n            <div slot=\"error\" class=\"image-slot\">\n              <i class=\"el-icon-picture-outline\"></i>\n            </div>\n          </el-image>\n        </template>\n      </el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.productTitle')\" min-width=\"200\">\n        <template slot-scope=\"scope\">\n          <el-link :href=\"scope.row.detailLink\" target=\"_blank\" type=\"primary\">\n            {{ scope.row.title }}\n          </el-link>\n        </template>\n      </el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.shop')\" width=\"120\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.shop ? scope.row.shop.name : '-' }}\n        </template>\n      </el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.originalPrice')\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.originalPrice\">\n            {{ formatPrice(scope.row.originalPrice) }}\n          </span>\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.salesPrice')\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.salesPrice\">\n            {{ formatPrice(scope.row.salesPrice) }}\n          </span>\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.commissionRate')\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.commission\">\n            {{ formatCommissionRate(scope.row.commission.rate) }}%\n          </span>\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.commissionAmount')\" width=\"100\">\n        <template slot-scope=\"scope\">\n          <span v-if=\"scope.row.commission\">\n            {{ scope.row.commission.amount }} {{ scope.row.commission.currency }}\n          </span>\n          <span v-else>-</span>\n        </template>\n      </el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.unitsSold')\" width=\"80\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.unitsSold || 0 }}\n        </template>\n      </el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.inventoryStatus')\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.hasInventory ? 'success' : 'danger'\" size=\"mini\">\n            {{ scope.row.hasInventory ? $t('affiliateProducts.hasInventory') : $t('affiliateProducts.noInventory') }}\n          </el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.saleRegion')\" width=\"80\">\n        <template slot-scope=\"scope\">\n          {{ scope.row.saleRegion || '-' }}\n        </template>\n      </el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.importStatus')\" width=\"80\">\n        <template slot-scope=\"scope\">\n          <el-tag :type=\"scope.row.isImported ? 'success' : 'info'\" size=\"mini\">\n            {{ scope.row.isImported ? $t('affiliateProducts.imported') : $t('affiliateProducts.notImported') }}\n          </el-tag>\n        </template>\n      </el-table-column>\n\n      <el-table-column :label=\"$t('affiliateProducts.action')\" width=\"180\" fixed=\"right\">\n        <template slot-scope=\"scope\">\n          <el-button\n            :type=\"scope.row.isImported ? 'success' : 'primary'\"\n            size=\"mini\"\n            @click=\"handleImportProduct(scope.row)\"\n            :disabled=\"scope.row.importing || scope.row.isImported\"\n          >\n            {{ scope.row.importing ? $t('affiliateProducts.importing') : (scope.row.isImported ? $t('affiliateProducts.imported') : $t('affiliateProducts.import')) }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            size=\"mini\"\n            @click=\"handleDeleteProduct(scope.row)\"\n            :disabled=\"scope.row.deleting\"\n          >\n            {{ scope.row.deleting ? $t('affiliateProducts.deleting') : $t('affiliateProducts.delete') }}\n          </el-button>\n        </template>\n      </el-table-column>\n    </el-table>\n\n    <!-- 分页 -->\n    <div v-if=\"hasSearched\" class=\"pagination-container\" style=\"margin-top: 20px; text-align: center;\">\n      <el-button\n        :disabled=\"!hasPrevPage\"\n        @click=\"handlePrevPage\"\n        size=\"small\"\n      >{{ $t('affiliateProducts.prevPage') }}</el-button>\n      <el-button\n        :disabled=\"!hasNextPage\"\n        @click=\"handleNextPage\"\n        size=\"small\"\n      >{{ $t('affiliateProducts.nextPage') }}</el-button>\n      <span style=\"margin-left: 20px;\">\n        {{ $t('affiliateProducts.pageSize') }}\n        <el-select v-model=\"searchForm.pageSize\" size=\"mini\" style=\"width: 80px;\" @change=\"handleSearch\">\n          <el-option label=\"10\" :value=\"10\"></el-option>\n          <el-option label=\"20\" :value=\"20\"></el-option>\n          <el-option label=\"50\" :value=\"50\"></el-option>\n        </el-select>\n      </span>\n      <span style=\"margin-left: 20px;\">\n        {{ $t('affiliateProducts.totalCount', { count: totalCount }) }}\n      </span>\n    </div>\n  </el-card>\n\n  <!-- 导入确认对话框 -->\n  <el-dialog\n    :title=\"currentImportProduct ? $t('affiliateProducts.importSingle') : $t('affiliateProducts.importBatch')\"\n    :visible.sync=\"importDialogVisible\"\n    width=\"500px\"\n    :close-on-click-modal=\"false\"\n  >\n    <div v-if=\"currentImportProduct\">\n      <p><strong>{{ $t('affiliateProducts.productTitle') }}：</strong>{{ currentImportProduct.title }}</p>\n      <p><strong>{{ $t('product.productId') }}：</strong>{{ currentImportProduct.id }}</p>\n    </div>\n    <div v-else>\n      <p><strong>{{ $t('affiliateProducts.selectedCount') }}</strong>{{ selectedProducts.length }}</p>\n    </div>\n    <div style=\"margin: 20px 0; padding: 15px; background-color: #f0f9ff; border: 1px solid #b3d8ff; border-radius: 4px;\">\n      <i class=\"el-icon-info\" style=\"color: #409eff; margin-right: 8px;\"></i>\n      <span style=\"color: #409eff;\">{{ $t('affiliateProducts.brandAutoDetect') }}</span>\n    </div>\n    <div slot=\"footer\" class=\"dialog-footer\">\n      <el-button @click=\"importDialogVisible = false\">{{ $t('affiliateProducts.cancel') }}</el-button>\n      <el-button type=\"primary\" @click=\"confirmImport\">{{ $t('affiliateProducts.confirmImport') }}</el-button>\n    </div>\n  </el-dialog>\n</div>\n", null]}