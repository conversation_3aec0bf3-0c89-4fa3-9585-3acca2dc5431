package com.genco.service.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.genco.common.exception.CrmebException;
import com.genco.common.model.system.SystemUserLevel;
import com.genco.common.model.user.User;
import com.genco.common.model.user.UserReferralRewardConfig;
import com.genco.common.response.PlatformRewardRulesResponse;
import com.genco.common.response.UserReferralStatsResponse;
import com.genco.common.token.FrontTokenComponent;
import com.genco.service.dao.UserReferralRewardConfigDao;
import com.genco.service.service.SystemConfigService;
import com.genco.service.service.SystemUserLevelService;
import com.genco.service.service.UserReferralRewardConfigService;
import com.genco.service.service.UserService;
import com.genco.service.service.UserBillService;
import com.genco.service.service.UserTaskClaimRecordService;
import com.genco.service.service.UserBrokerageRecordService;
import com.genco.common.model.user.UserBill;
import com.genco.common.model.user.UserTaskClaimRecord;
import com.genco.common.model.user.UserBrokerageRecord;
import com.genco.common.constants.BrokerageRecordConstants;
import com.genco.common.response.UserRewardStats;
import com.genco.common.constants.Constants;
import com.genco.common.constants.TaskConstants;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;

/**
 * 用户拉新奖励配置服务实现类
 */
@Slf4j
@Service
public class UserReferralRewardConfigServiceImpl extends ServiceImpl<UserReferralRewardConfigDao, UserReferralRewardConfig> implements UserReferralRewardConfigService {

    @Autowired
    private UserReferralRewardConfigDao userReferralRewardConfigDao;

    @Autowired
    private UserService userService;

    @Autowired
    private SystemUserLevelService systemUserLevelService;

    @Autowired
    private SystemConfigService systemConfigService;

    @Autowired
    private FrontTokenComponent tokenComponent;

    @Autowired
    private UserBillService userBillService;

    @Autowired
    private UserTaskClaimRecordService userTaskClaimRecordService;

    @Autowired
    private UserBrokerageRecordService userBrokerageRecordService;

    @Autowired
    private TransactionTemplate transactionTemplate;

    /**
     * 获取当前用户的拉新奖励配置
     * 优先级：银级会员配置 > 平台级配置
     */
    @Override
    public UserReferralRewardConfig getCurrentUserReferralRewardConfig() {
        Integer uid = tokenComponent.getUserId();
        if (uid == null) {
            throw new CrmebException("登录信息已过期，请重新登录！");
        }
        return getUserReferralRewardConfig(uid);
    }

    /**
     * 获取银级会员为下级设置的拉新奖励配置
     */
    @Override
    public UserReferralRewardConfig getMyReferralRewardConfig() {
        Integer currentUid = tokenComponent.getUserId();
        if (currentUid == null) {
            throw new CrmebException("登录信息已过期，请重新登录！");
        }

        // 验证权限
        if (!checkConfigPermission(currentUid)) {
            throw new CrmebException("只有银级会员才能查看自己的配置");
        }

        // 查找当前银级会员的配置
        LambdaQueryWrapper<UserReferralRewardConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserReferralRewardConfig::getUid, currentUid);
        UserReferralRewardConfig config = getOne(wrapper);

        if (config == null) {
            // 返回默认配置
            config = new UserReferralRewardConfig();
            config.setUid(currentUid);
            config.setReferralCount(5);
            config.setFirstOrderCount(3);
            config.setRewardAmount(new BigDecimal("25000"));
            config.setStatus(1);
        }

        return config;
    }

    /**
     * 保存或更新银级会员为下级设置的拉新奖励配置
     */
    @Override
    public Boolean saveOrUpdateMyConfig(UserReferralRewardConfig config) {
        Integer currentUid = tokenComponent.getUserId();
        if (currentUid == null) {
            throw new CrmebException("登录信息已过期，请重新登录！");
        }

        // 验证权限
        if (!checkConfigPermission(currentUid)) {
            throw new CrmebException("只有银级会员才能配置下级的拉新奖励");
        }

        // 设置用户ID
        config.setUid(currentUid);

        // 验证首单数量不能大于拉新数量
        if (config.getFirstOrderCount() > config.getReferralCount()) {
            throw new CrmebException("首单数量不能大于拉新数量");
        }

        return saveOrUpdateConfig(config);
    }

    /**
     * 验证当前用户是否有权限配置拉新奖励
     */
    @Override
    public Boolean checkCurrentUserConfigPermission() {
        Integer currentUid = tokenComponent.getUserId();
        if (currentUid == null) {
            return false;
        }
        return checkConfigPermission(currentUid);
    }

    /**
     * 获取用户的拉新奖励配置
     * 优先级：银级会员配置 > 平台级配置
     */
    @Override
    public UserReferralRewardConfig getUserReferralRewardConfig(Integer uid) {
        // 1. 查找用户信息
        User user = userService.getById(uid);
        if (user == null) {
            throw new CrmebException("用户不存在");
        }

        // 2. 查找用户的上级
        if (user.getSpreadUid() != null && user.getSpreadUid() > 0) {
            User parentUser = userService.getById(user.getSpreadUid());
            if (parentUser != null) {
                // 3. 判断上级是否是银级及以上会员
                SystemUserLevel parentLevel = systemUserLevelService.getByLevelId(parentUser.getLevel());
                if (parentLevel != null && parentLevel.getGrade() >= 2) {
                    // 4. 查找银级会员的拉新奖励配置
                    UserReferralRewardConfig parentConfig = userReferralRewardConfigDao.selectByUid(parentUser.getUid());
                    if (parentConfig != null && parentConfig.getStatus() == 1) {
                        return parentConfig;
                    }
                }
            }
        }

        // 5. 使用平台级配置
        return getPlatformReferralRewardConfig();
    }

    /**
     * 保存或更新用户的拉新奖励配置（私有方法）
     */
    private Boolean saveOrUpdateConfig(UserReferralRewardConfig config) {
        // 1. 验证权限
        if (!checkConfigPermission(config.getUid())) {
            throw new CrmebException("只有银级会员才能配置拉新奖励");
        }

        // 2. 查找是否已有配置
        LambdaQueryWrapper<UserReferralRewardConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserReferralRewardConfig::getUid, config.getUid());
        UserReferralRewardConfig existConfig = getOne(wrapper);

        if (existConfig != null) {
            // 更新
            config.setId(existConfig.getId());
            config.setUpdateTime(new Date());
            return updateById(config);
        } else {
            // 新增
            config.setCreateTime(new Date());
            config.setUpdateTime(new Date());
            return save(config);
        }
    }

    /**
     * 验证用户是否有权限配置拉新奖励
     */
    @Override
    public Boolean checkConfigPermission(Integer uid) {
        User user = userService.getById(uid);
        if (user == null) {
            return false;
        }

        // 检查是否是银级及以上会员
        SystemUserLevel userLevel = systemUserLevelService.getByLevelId(user.getLevel());
        return userLevel != null && userLevel.getGrade() >= 2;
    }

    /**
     * 获取平台级拉新奖励配置
     */
    private UserReferralRewardConfig getPlatformReferralRewardConfig() {
        // 从系统配置中获取平台级配置（formId=144）
        HashMap<String, String> platformConfig = systemConfigService.info(144);

        UserReferralRewardConfig config = new UserReferralRewardConfig();
        config.setUid(0); // 平台级配置用0表示
        config.setReferralCount(Integer.parseInt(platformConfig.getOrDefault("referral_count", "0")));
        config.setFirstOrderCount(Integer.parseInt(platformConfig.getOrDefault("first_order_count", "0")));
        config.setRewardAmount(new BigDecimal(platformConfig.getOrDefault("reward_amount", "0")));
        config.setStatus(1);

        return config;
    }

    /**
     * 获取平台级多语言奖励规则
     */
    @Override
    public PlatformRewardRulesResponse getPlatformRewardRules() {
        // 从系统配置中获取平台级多语言规则（formId=144）
        HashMap<String, String> platformConfig = systemConfigService.info(144);

        PlatformRewardRulesResponse response = new PlatformRewardRulesResponse();
        response.setRewardRuleZh(platformConfig.getOrDefault("reward_rule_zh", ""));
        response.setRewardRuleEn(platformConfig.getOrDefault("reward_rule_en", ""));
        response.setRewardRuleId(platformConfig.getOrDefault("reward_rule_id", ""));

        return response;
    }

    /**
     * 获取当前用户拉新统计数据
     */
    @Override
    public UserReferralStatsResponse getCurrentUserReferralStats() {
        Integer uid = tokenComponent.getUserId();
        if (uid == null) {
            throw new CrmebException("登录信息已过期，请重新登录！");
        }

        // 获取用户信息
        User user = userService.getById(uid);
        if (user == null) {
            throw new CrmebException("用户不存在");
        }

        UserReferralStatsResponse response = new UserReferralStatsResponse();

        // 获取用户佣金余额（任务中心应该显示佣金余额而不是现金余额）
        response.setUserBalance(user.getBrokeragePrice() != null ? user.getBrokeragePrice() : BigDecimal.ZERO);

        // 获取累计邀请人数（下级用户数量）
        response.setTotalReferralCount(user.getSpreadCount() != null ? user.getSpreadCount() : 0);

        // 获取累计首单数（通过查询下级用户的首单情况）
        Integer totalFirstOrderCount = userReferralRewardConfigDao.getTotalFirstOrderCountBySpreadUid(uid);
        response.setTotalFirstOrderCount(totalFirstOrderCount != null ? totalFirstOrderCount : 0);

        // 从任务兑换记录表统计已兑换数量
        UserRewardStats redeemedStats = userTaskClaimRecordService.getRedeemedStats(uid, TaskConstants.TASK_TYPE_INVITE_FIRST_ORDER);
        response.setRedeemedReferralCount(redeemedStats.getReferralCount());
        response.setRedeemedFirstOrderCount(redeemedStats.getFirstOrderCount());

        return response;
    }

    /**
     * 领取邀请首单奖励（批量领取模式）
     */
    @Override
    public Boolean claimInviteFirstOrderReward() {
        Integer uid = tokenComponent.getUserId();
        if (uid == null) {
            throw new CrmebException("登录信息已过期，请重新登录！");
        }

        // 1. 获取用户信息
        User user = userService.getById(uid);
        if (user == null) {
            throw new CrmebException("用户不存在");
        }

        // 2. 获取用户的拉新奖励配置
        UserReferralRewardConfig config = getUserReferralRewardConfig(uid);
        if (config == null) {
            throw new CrmebException("未找到奖励配置");
        }

        // 3. 使用配置信息（不再需要创建个人配置记录）
        UserReferralRewardConfig userConfig = config;

        // 4. 获取用户当前的邀请和首单数据
        int totalReferralCount = user.getSpreadCount() != null ? user.getSpreadCount() : 0;
        int totalFirstOrderCount = userReferralRewardConfigDao.getTotalFirstOrderCountBySpreadUid(uid);

        // 5. 从任务兑换记录表获取已兑换的数量
        UserRewardStats redeemedStats = userTaskClaimRecordService.getRedeemedStats(uid, TaskConstants.TASK_TYPE_INVITE_FIRST_ORDER);
        int redeemedReferralCount = redeemedStats.getReferralCount();
        int redeemedFirstOrderCount = redeemedStats.getFirstOrderCount();

        // 6. 计算可用于兑换的数量
        int availableReferralCount = totalReferralCount - redeemedReferralCount;
        int availableFirstOrderCount = totalFirstOrderCount - redeemedFirstOrderCount;

        // 7. 计算可以兑换多少组奖励
        int maxRedeemableGroups = Math.min(
            availableReferralCount / userConfig.getReferralCount(),
            availableFirstOrderCount / userConfig.getFirstOrderCount()
        );

        if (maxRedeemableGroups <= 0) {
            throw new CrmebException("暂无可领取的奖励。需要邀请" + userConfig.getReferralCount() + "人且" + userConfig.getFirstOrderCount() + "人完成首单才能领取1次奖励");
        }

        // 8. 批量发放奖励
        return transactionTemplate.execute(status -> {
            try {
                BigDecimal totalRewardAmount = userConfig.getRewardAmount().multiply(new BigDecimal(maxRedeemableGroups));

                // 创建用户账单记录（记录到现金账单中，方便用户在交易记录中查看）
                UserBill bill = new UserBill();
                bill.setUid(uid);
                bill.setLinkId("invite_first_order_reward_batch");
                bill.setPm(1); // 1 = 获得
                bill.setTitle("邀请首单奖励");
                bill.setCategory(Constants.USER_BILL_CATEGORY_MONEY); // 现金类别，用户可在交易记录中看到
                bill.setType("invite_first_order_reward");
                bill.setNumber(totalRewardAmount);
                bill.setBalance(user.getBrokeragePrice().add(totalRewardAmount)); // 显示更新后的佣金余额
                bill.setMark("批量领取" + maxRedeemableGroups + "组奖励，每组条件：邀请" + userConfig.getReferralCount() + "人且" + userConfig.getFirstOrderCount() + "人完成首单");
                bill.setStatus(1); // 1 = 有效
                bill.setCreateTime(new Date());

                // 保存账单记录
                boolean billSaved = userBillService.save(bill);
                if (!billSaved) {
                    throw new RuntimeException("账单记录保存失败");
                }

                // 更新用户佣金余额
                boolean balanceUpdated = userService.operationBrokerage(uid, totalRewardAmount, user.getBrokeragePrice(), "add");
                if (!balanceUpdated) {
                    throw new RuntimeException("用户佣金余额更新失败");
                }

                // 创建佣金记录
                UserBrokerageRecord brokerageRecord = new UserBrokerageRecord();
                brokerageRecord.setUid(uid);
                brokerageRecord.setLinkId("invite_first_order_reward_batch");
                brokerageRecord.setLinkType(BrokerageRecordConstants.BROKERAGE_RECORD_LINK_TYPE_YUE);
                brokerageRecord.setType(BrokerageRecordConstants.BROKERAGE_RECORD_TYPE_ADD);
                brokerageRecord.setTitle("邀请首单奖励");
                brokerageRecord.setPrice(totalRewardAmount);
                brokerageRecord.setBalance(user.getBrokeragePrice().add(totalRewardAmount));
                brokerageRecord.setMark("批量领取" + maxRedeemableGroups + "组奖励，每组条件：邀请" + userConfig.getReferralCount() + "人且" + userConfig.getFirstOrderCount() + "人完成首单");
                brokerageRecord.setStatus(BrokerageRecordConstants.BROKERAGE_RECORD_STATUS_COMPLETE);
                brokerageRecord.setCreateTime(new Date());

                // 保存佣金记录
                boolean brokerageRecordSaved = userBrokerageRecordService.save(brokerageRecord);
                if (!brokerageRecordSaved) {
                    throw new RuntimeException("佣金记录保存失败");
                }

                // 创建任务兑换记录
                UserTaskClaimRecord claimRecord = new UserTaskClaimRecord();
                claimRecord.setUid(uid);
                claimRecord.setTaskType(TaskConstants.TASK_TYPE_INVITE_FIRST_ORDER);
                claimRecord.setTaskConfigSnapshot(JSON.toJSONString(userConfig));
                claimRecord.setClaimGroups(maxRedeemableGroups);

                // 计算累计兑换组数
                Integer previousTotalGroups = userTaskClaimRecordService.getTotalClaimedGroups(uid, TaskConstants.TASK_TYPE_INVITE_FIRST_ORDER);
                claimRecord.setTotalClaimGroups(previousTotalGroups + maxRedeemableGroups);

                claimRecord.setReferralCountRequired(userConfig.getReferralCount());
                claimRecord.setFirstOrderCountRequired(userConfig.getFirstOrderCount());
                claimRecord.setReferralCountUsed(maxRedeemableGroups * userConfig.getReferralCount());
                claimRecord.setFirstOrderCountUsed(maxRedeemableGroups * userConfig.getFirstOrderCount());
                claimRecord.setRewardAmountPerGroup(userConfig.getRewardAmount());
                claimRecord.setTotalRewardAmount(totalRewardAmount);
                claimRecord.setBillId(bill.getId());
                claimRecord.setClaimTime(new Date());
                claimRecord.setStatus(TaskConstants.TASK_STATUS_ACTIVE);
                claimRecord.setRemark("批量领取" + maxRedeemableGroups + "组奖励");
                claimRecord.setCreateTime(new Date());
                claimRecord.setUpdateTime(new Date());

                boolean claimRecordSaved = userTaskClaimRecordService.save(claimRecord);
                if (!claimRecordSaved) {
                    throw new RuntimeException("任务兑换记录保存失败");
                }

                log.info("用户{}成功批量领取{}组邀请首单奖励，总金额：{}", uid, maxRedeemableGroups, totalRewardAmount);
                return true;
            } catch (Exception e) {
                log.error("用户{}领取邀请首单奖励失败：{}", uid, e.getMessage(), e);
                throw new RuntimeException("奖励发放失败：" + e.getMessage());
            }
        });
    }
}
