{"remainingRequest": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js??ref--13-0!C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\TagsView\\index.vue", "dependencies": [{"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\src\\layout\\components\\TagsView\\index.vue", "mtime": 1754375668239}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1754052680463}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1754052680446}, {"path": "C:\\Users\\<USER>\\Desktop\\新建文件夹 (3)\\adminUI\\node_modules\\eslint-loader\\index.js", "mtime": 1754052677050}], "contextDependencies": [], "result": ["import { render, staticRenderFns } from \"./index.vue?vue&type=template&id=fac8ca64&scoped=true\"\nimport script from \"./index.vue?vue&type=script&lang=js\"\nexport * from \"./index.vue?vue&type=script&lang=js\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=fac8ca64&lang=scss&scoped=true\"\nimport style1 from \"./index.vue?vue&type=style&index=1&id=fac8ca64&lang=scss&scoped=true\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../node_modules/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"fac8ca64\",\n  null\n  \n)\n\n/* hot reload */\nif (module.hot) {\n  var api = require(\"C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹 (3)\\\\adminUI\\\\node_modules\\\\vue-hot-reload-api\\\\dist\\\\index.js\")\n  api.install(require('vue'))\n  if (api.compatible) {\n    module.hot.accept()\n    if (!api.isRecorded('fac8ca64')) {\n      api.createRecord('fac8ca64', component.options)\n    } else {\n      api.reload('fac8ca64', component.options)\n    }\n    module.hot.accept(\"./index.vue?vue&type=template&id=fac8ca64&scoped=true\", function () {\n      api.rerender('fac8ca64', {\n        render: render,\n        staticRenderFns: staticRenderFns\n      })\n    })\n  }\n}\ncomponent.options.__file = \"src/layout/components/TagsView/index.vue\"\nexport default component.exports"]}